package com.theolympiastone.club.my.bb;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jfinal.aop.Before;
import com.jfinal.aop.Inject;
import com.jfinal.kit.JsonKit;
import com.jfinal.kit.Kv;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import com.jfinal.upload.UploadFile;
import com.theolympiastone.club.common.LayGsonBuilder;
import com.theolympiastone.club.common.LayRecordData;
import com.theolympiastone.club.common.controller.BaseController;
import com.theolympiastone.club.common.interceptor.FrontAuthInterceptor;
import com.theolympiastone.club.common.interceptor.ManagerAuthInterceptor;
import com.theolympiastone.club.common.kit.TipHelperKit;
import com.theolympiastone.club.common.kit.XmSelect;
import com.theolympiastone.club.common.model.Ndmb;
import com.theolympiastone.club.common.model.NdmbZs;
import com.theolympiastone.club.my.account.MyAccountService;
import com.theolympiastone.club.my.ndmb.MyNdmbService;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;

import java.io.IOException;
import java.math.BigDecimal;
import java.sql.CallableStatement;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.theolympiastone.club.common.kit.StringKit.*;

@Before({FrontAuthInterceptor.class})
public class MyBbController extends BaseController {
    @Inject
    MyAccountService srv;
    @Inject
    private MyNdmbService ndmbService;

    private static Map<String, String> getMap() {
        Map<String, String> columMap = Maps.newLinkedHashMap();
        columMap.put("mbe", "年度总目标");
        columMap.put("xzmbe", "新增目标");
        columMap.put("Z0", "盈亏平衡点Z0");
        columMap.put("M0", "保证目标M0");
        columMap.put("M1", "进步目标M1");
        columMap.put("M2", "优秀目标M2");
        columMap.put("M3", "超级目标M3");
        columMap.put("M4", "卓越目标M4");
        columMap.put("xzkhs", "新增客户数");
        columMap.put("xzddjermb", "新增客户的订单金额(RMB)");
        columMap.put("xzml", "新增客户的毛利");
        columMap.put("zddjermb", "总订单金额(RMB)");
        columMap.put("lmbce", "离保障目标差额");
        columMap.put("lxzce", "离新增目标差额");
        columMap.put("zml", "总毛利");
        columMap.put("spe", "金额索赔");
        columMap.put("spcz", "重做索赔");
        columMap.put("tcsp", "提成索赔");
        columMap.put("jbgz", "基本工资");
        columMap.put("tc", "提成");
        columMap.put("tqddjermb", "上年度同期订单金额(RMB)");
        columMap.put("tqml", "上年度同期毛利");
        columMap.put("tqspe", "上年度同期索赔");
        return columMap;
    }

    private static Map<String, String> getColumMap(String tq, String qn, String dqn) {
        Map<String, String> columMap = Maps.newLinkedHashMap();
        columMap.put("sjddje", "当期实际销售额");
        // columMap.put("zddjermb", "提成销售额");
        columMap.put("zsp", "总索赔");
        columMap.put("zml", "毛利");
        columMap.put("tqwz0", tq + "工资基数");
        columMap.put("wz0", "工资基数");
        columMap.put("Z0", "基数");
        // columMap.put("tqddjermb", "同期提成销售额");
        columMap.put("tqsjddje", tq + "实际销售额");
        columMap.put("qnsjddje", qn + "实际销售额");
        columMap.put("dqnsjddje", dqn + "实际销售额");
        columMap.put("jszz", "基数增长");
        columMap.put("tqzz", "同期增长");
        columMap.put("Z0JX", "基数绩效");
        columMap.put("M0", "保障目标M0");
        columMap.put("M0JX", "M0绩效");
        columMap.put("tc", "已提成");
        columMap.put("M1", "进步目标M1");
        columMap.put("M1JX", "M1绩效");
        columMap.put("M2", "优秀目标M2");
        columMap.put("M2JX", "M2绩效");
        columMap.put("M3", "超级目标M3");
        columMap.put("M3JX", "M3绩效");
        columMap.put("M4", "卓越目标M4");
        columMap.put("M4JX", "M4绩效");
        columMap.put("ZJX", "总绩效");
        columMap.put("MBJX", "目标绩效");
        return columMap;
    }

    public void khfx() {
        render("khfx.html");
    }

    public void mstgData() {
        keepPara();
        String q = getPara("q", "");
        String sc = getPara("sc", "");
        String szid = getPara("szid", "");
        String myfs = getPara("myfs", "");
        String px = getPara("px", " order by mc,sc,myfs,clmc asc");
        setAttr("sc", sc);
        setAttr("szid", szid);
        setAttr("myfs", myfs);
        setAttr("px", px);
        String today = yyyy_MM_dd();
        List<Record> records = Db.find("select hb, ifnull(w.xs, ifnull(l.xs, '1')) xs from " +
                "(select distinct hb from whkhl union select distinct hb from lrhl) h " +
                "left join whkhl w on h.hb = w.hb and ? >= w.kssj and ? <= w.jssj " +
                "left join lrhl l on h.hb = l.hb and ? >= l.kssj and ? <= l.jssj", today, today, today, today);
        Map<String, String> hlMap = Maps.newHashMap();
        for (Record record : records) {
            hlMap.put(record.getStr("hb"), record.getStr("xs"));
        }
        setAttr("szList", Db.find("select id,mc from cl order by mc"));
        String sql = "";
        if (!StringUtils.isEmpty(q)) {
            sql += " and (mc like '%" + q + "%' or sc like '%" + q + "%' or myfs like '%" + q + "%' or bz like '%" + q
                    + "%' ) ";
        }
        if (!StringUtils.isEmpty(sc)) {
            sql += " and sc like '%" + sc + "%' ";
        }
        if (!StringUtils.isEmpty(myfs)) {
            sql += " and myfs like '%" + myfs + "%' ";
        }
        if (!StringUtils.isEmpty(szid)) {
            sql += " and szid=" + szid + " ";
        }
        String rSql = "select * from (select z.*, " +
                "                      c.id                             clid, " +
                "                      c.mc                             clmc, " +
                "                      c.ywmc                           ywmc, " +
                "                      c.bz                             qtmz, " +
                "                      round(z.jg)              rmb, " +
                "                      round(z.jg / " + hlMap.get("美元") + ")              usd, " +
                "                      round(z.jg / " + hlMap.get("欧元") + ")      eur, " +
                "                      round(z.jg / " + hlMap.get("澳元") + ")      aud, " +
                "                      round(36 * z.jg)                 lfrmb, " +
                "                      round(36 * z.jg / " + hlMap.get("美元") + ")         lfusd, " +
                "                      round(36 * z.jg / " + hlMap.get("欧元") + ") lfeur, " +
                "                      round(36 * z.jg / " + hlMap.get("澳元") + ") lfaud " +
                "               from mstgj z, " +
                "                    cl c " +
                "               where z.zt = '展示' " +
                "                 and z.dw = 'CAI' " +
                "                 and z.szid = c.id " +
                "               union all " +
                "               select z.*, " +
                "                      c.id                             clid, " +
                "                      c.mc                             clmc, " +
                "                      c.ywmc                           ywmc, " +
                "                      c.bz                             qtmz, " +
                "                      round(z.jg / 36)              rmb, " +
                "                      round(z.jg / 36 / " + hlMap.get("美元") + ")              usd, " +
                "                      round(z.jg / 36 / " + hlMap.get("欧元") + ")      eur, " +
                "                      round(z.jg / 36 /" + hlMap.get("澳元") + ")      aud, " +
                "                      round(z.jg)                 lfrmb, " +
                "                      round(z.jg / " + hlMap.get("美元") + ")         lfusd, " +
                "                      round(z.jg / " + hlMap.get("欧元") + ") lfeur, " +
                "                      round(z.jg / " + hlMap.get("澳元") + ") lfaud " +
                "               from mstgj z, cl c " +
                "               where z.zt = '展示' " +
                "                 and z.dw = 'M3' " +
                "                 and z.szid = c.id) a where 1=1 " + sql + px;
        renderText(LayGsonBuilder.create().toJson(new LayRecordData(Db.find(rSql))));
    }

    public void mstg() {
        setAttr("szList", Db.find("select id,mc from cl order by mc"));
        render("mstgj.html");
    }

    public void cl() {
        Integer id = getInt("id");
        Record x = Db.findFirst("select * from cl where id=?;", id);
        setAttr("x", x);
        render("cl.html");
    }

    @Before(ManagerAuthInterceptor.class)
    public void cb() {
        keepPara();
        String kssj = getPara("kssj");
        String jssj = getPara("jssj");
        String ywy = getPara("ywy");
        String khid = getPara("khid");
        setAttr("khList", Db.find("select id,jc from kh order by jc"));
        setAttr("ywList", Db.find("select id,xm from account where id in (select ywyid from tc)  order by xm"));
        if (!StringUtils.isEmpty(kssj) && !StringUtils.isEmpty(jssj)) {
            setAttr("records", Db.find(
                    Db.getSqlPara("bb.khqk", Kv.by("kssj", kssj).set("jssj", jssj).set("ywy", ywy).set("khid", khid))));
            setAttr("totalRecords", Db.find(Db.getSqlPara("bb.khqk_hz",
                    Kv.by("kssj", kssj).set("jssj", jssj).set("ywy", ywy).set("khid", khid))));
        }
        render("cb.html");
    }

    @Before(ManagerAuthInterceptor.class)
    public void yj() {
        DateTime dateTime = DateTime.now();
        String defaultKssj = dateTime.toString("yyyy-01-01");
        String defaultJssj = dateTime.toString("yyyy-12-31");
        if (dateTime.toString("yyyy-MM-dd").compareTo(dateTime.toString("yyyy-03-01")) < 0) {
            defaultKssj = plusYear(defaultKssj, -1);
            defaultJssj = plusYear(defaultJssj, -1);
        }
        String kssj = getPara("kssj", defaultKssj);
        String jssj = getPara("jssj", defaultJssj);
        String nf = kssj.substring(0, 4);
        String ksyf = kssj.substring(0, 7);
        String jsyf = jssj.substring(0, 7);
        String tqkssj = plusYear(kssj, -1);
        String tqjssj = plusYear(jssj, -1);
        setAttr("kssj", kssj);
        setAttr("jssj", jssj);
        Map<String, Map<String, String>> resultMap = Maps.newLinkedHashMap();
        List<Record> accountRecordList = Db.find("select * from account order by userName");
        Map<String, Record> accountMap = Maps.newHashMap();
        accountRecordList.forEach((record) -> accountMap.put(record.getStr("userName"), record));

        // 新增客户
        List<Record> khsRecord = Db.find("select hzywy,count(*) khs " +
                "from kh " +
                "where hzsj >= ? and hzsj <= ? group by hzywy", kssj, jssj);
        khsRecord.forEach((r) -> {
            String ywy = accountMap.get(r.getStr("hzywy")).getStr("xm");
            Map<String, String> map = resultMap.getOrDefault(ywy, Maps.newHashMap());
            map.put("xzkhs", r.getStr("khs"));
            resultMap.put(ywy, map);
        });

        // 新增客户销售额(RMB) 新增客户毛利
        List<Record> xzRecord = Db.find(
                "select a.userName, concat(round(sum(ifnull(d.hkermb,0)),0)) sjddje, concat(round(sum((ifnull(d.ddje,0) + ifnull(d.dduyf, 0) + ifnull(d.sjdj, 0))* ifnull(l.xs, 0)), 0)) ddjermb, concat(round(sum(ifnull(d.ml, 0)), 0)) ml "
                        +
                        "from dd d left join lrhl l on d.hb = l.hb and l.kssj <= d.cq and l.jssj >= d.cq, vw_mrtcxs_zhcf t, account a "
                        +
                        "where d.kh in (select id from kh where hzsj >= ? and hzsj <= ?) " +
                        "  and d.lx='正常订单' " +
                        "  and d.kh = t.khid and t.DAY_SHORT_DESC=d.cq " +
                        "  and t.ywyid = a.id " +
                        "  and d.cq >= ? " +
                        "  and d.cq <= ? " +
                        "group by a.userName;",
                kssj, jssj, kssj, jssj);
        xzRecord.forEach((r) -> {
            String ywy = accountMap.get(r.getStr("userName")).getStr("xm");
            Map<String, String> map = resultMap.getOrDefault(ywy, Maps.newHashMap());
            map.put("xzddjermb", r.getStr("ddjermb"));
            map.put("xzsjddje", r.getStr("sjddje"));
            map.put("xzml", r.getStr("ml"));
            resultMap.put(ywy, map);
        });

        // 总销售额(RMB) 总毛利
        List<Record> zRecord = Db.find(
                "select a.userName, concat(round(sum(ifnull(d.hkermb,0)),0)) sjddje, concat(round(sum((ifnull(d.ddje,0) + ifnull(d.dduyf, 0) + ifnull(d.sjdj, 0))* ifnull(l.xs, 0)), 0)) ddjermb, concat(round(sum(ifnull(d.ml, 0)), 0)) ml, concat(round(sum(ifnull(spe, 0)*whkhl), 0)) spe, concat(round(sum(ifnull(spcz, 0)*whkhl), 0)) spcz, concat(round(sum(ifnull(tcsp, 0)*whkhl), 0)) tcsp "
                        +
                        "from dd d left join lrhl l on d.hb = l.hb and l.kssj <= d.cq and l.jssj >= d.cq, vw_mrtcxs_zhcf t, account a "
                        +
                        "where d.lx='正常订单' " +
                        "  and d.kh = t.khid and t.DAY_SHORT_DESC=d.cq " +
                        "  and t.ywyid = a.id " +
                        "  and d.cq >= ? " +
                        "  and d.cq <= ? " +
                        "group by a.userName;",
                kssj, jssj);
        zRecord.forEach((r) -> {
            String ywy = accountMap.get(r.getStr("userName")).getStr("xm");
            Map<String, String> map = resultMap.getOrDefault(ywy, Maps.newHashMap());
            map.put("zddjermb", r.getStr("ddjermb"));
            map.put("sjddje", r.getStr("sjddje"));
            map.put("zml", r.getStr("ml"));
            map.put("spe", r.getStr("spe"));
            map.put("spcz", r.getStr("spcz"));
            map.put("tcsp", r.getStr("tcsp"));
            resultMap.put(ywy, map);
        });

        // 基本工资 提成
        List<Record> gzRecord = Db.find(
                "select a.userName, concat(round(sum(ifnull(zj, 0) -ifnull(ztc, 0)), 0)) jbgz, concat(round(sum(ifnull(ztc, 0)), 0)) ztc "
                        +
                        "from gz g, account a " +
                        "where g.yf >= ? " +
                        "  and g.yf <= ? and g.xm=a.xm " +
                        "group by a.userName",
                ksyf, jsyf);
        gzRecord.forEach((r) -> {
            String ywy = accountMap.get(r.getStr("userName")).getStr("xm");
            Map<String, String> map = resultMap.getOrDefault(ywy, Maps.newHashMap());
            map.put("jbgz", r.getStr("jbgz"));
            map.put("tc", r.getStr("ztc"));
            resultMap.put(ywy, map);
        });

        // 同期总销售额(RMB) 总毛利
        List<Record> tqRecord = Db.find(
                "select a.userName, concat(round(sum(ifnull(d.hkermb,0)),0)) sjddje, concat(round(sum((ifnull(d.ddje,0) + ifnull(d.dduyf, 0) + ifnull(d.sjdj, 0))* ifnull(l.xs, 0)), 0)) ddjermb, concat(round(sum(ifnull(d.ml, 0)), 0)) ml, concat(round(sum(ifnull(spe, 0)), 0), max(d.hb)) spe "
                        +
                        "from dd d left join lrhl l on d.hb = l.hb and l.kssj <= d.cq and l.jssj >= d.cq, vw_mrtcxs_zhcf t, account a "
                        +
                        "where d.lx='正常订单' " +
                        "  and d.kh = t.khid and t.DAY_SHORT_DESC=d.cq " +
                        "  and t.ywyid = a.id " +
                        "  and d.cq >= ? " +
                        "  and d.cq <= ? " +
                        "group by a.userName;",
                tqkssj, tqjssj);
        tqRecord.forEach((r) -> {
            String ywy = accountMap.get(r.getStr("userName")).getStr("xm");
            Map<String, String> map = resultMap.getOrDefault(ywy, Maps.newHashMap());
            map.put("tqddjermb", r.getStr("ddjermb"));
            map.put("tqsjddje", r.getStr("sjddje"));
            map.put("tqml", r.getStr("ml"));
            map.put("tqspe", r.getStr("spe"));
            resultMap.put(ywy, map);
        });

        List<Record> ndmbRecord = Db.find(
                " select ywy, concat(ifnull(mbe, 0)) mbe, concat(ifnull(xzmbe, 0)) xzmbe, concat(ifnull(z0, 0)) z0, concat(ifnull(m0, 0)) m0, concat(ifnull(m1, 0)) m1, concat(ifnull(m2, 0)) m2, concat(ifnull(m3, 0)) m3, concat(ifnull(m4, 0)) m4, concat(ifnull(zjx, 0)) zjx, "
                        +
                        " concat(ifnull(z0jx, 0)) z0jx,concat(ifnull(m0jx, 0)) m0jx, concat(ifnull(m1jx, 0)) m1jx, concat(ifnull(m2jx, 0)) m2jx, concat(ifnull(m3jx, 0)) m3jx, concat(ifnull(m4jx, 0)) m4jx, concat(ifnull(mbjx, 0)) mbjx "
                        +
                        " from ndmb " +
                        " where nf= ? ",
                nf);
        ndmbRecord.forEach((r) -> {
            String ywy = r.getStr("ywy");
            Map<String, String> map = resultMap.getOrDefault(ywy, Maps.newHashMap());
            map.put("mbe", r.getStr("mbe"));
            map.put("xzmbe", r.getStr("xzmbe"));
            map.put("Z0", r.getStr("z0"));
            map.put("Z0JX", r.getStr("z0jx"));
            map.put("M0", r.getStr("m0"));
            map.put("M1", r.getStr("m1"));
            map.put("M2", r.getStr("m2"));
            map.put("M3", r.getStr("m3"));
            map.put("M4", r.getStr("m4"));
            map.put("M0JX", r.getStr("m0jx"));
            map.put("M1JX", r.getStr("m1jx"));
            map.put("M2JX", r.getStr("m2jx"));
            map.put("M3JX", r.getStr("m3jx"));
            map.put("M4JX", r.getStr("m4jx"));
            map.put("ZJX", r.getStr("zjx"));
            resultMap.put(ywy, map);
        });

        Map<String, String> columMap = getMap();

        Set<String> keySet = Sets.newLinkedHashSet(resultMap.keySet());
        for (String key : keySet) {
            Map<String, String> map = resultMap.get(key);
            map.put("lmbce", d2s(s2d(map.get("zddjermb")) - s2d(map.get("mbe")), 0));
            map.put("lxzce", d2s(s2d(map.get("xzddjermb")) - s2d(map.get("xzmbe")), 0));
            resultMap.put(key, map);
        }
        for (String key : keySet) {
            for (String columKey : columMap.keySet()) {
                Map<String, String> hjMap = resultMap.getOrDefault("合计", Maps.newHashMap());
                hjMap.put(columKey, d2s(s2d(resultMap.get(key).get(columKey)) + s2d(hjMap.get(columKey)), 0));
                resultMap.put("合计", hjMap);
            }
        }

        setAttr("columMap", columMap);
        setAttr("resultMap", resultMap);
        set("nf", nf);
        render("yj.html");
    }

    public String getYwy(Map<String, Record> accountMap, Record record, String key) {
        if (accountMap == null) {
            return StringUtils.EMPTY;
        }
        if (record == null) {
            return StringUtils.EMPTY;
        }
        String str = record.getStr(key);
        if (StringUtils.isEmpty(key)) {
            return StringUtils.EMPTY;
        }
        Record accountRecord = accountMap.get(str);
        if (accountRecord == null) {
            return StringUtils.EMPTY;
        }
        return trueString(accountRecord.getStr("xm"), str);
    }

    @Before(ManagerAuthInterceptor.class)
    public void ykph() {
        DateTime dateTime = DateTime.now();
        String defaultKssj = dateTime.toString("yyyy-01-01");
        String defaultJssj = dateTime.toString("yyyy-12-31");
        if (dateTime.toString("yyyy-MM-dd").compareTo(dateTime.toString("yyyy-03-01")) < 0) {
            defaultKssj = plusYear(defaultKssj, -1);
            defaultJssj = plusYear(defaultJssj, -1);
        }
        String kssj = getPara("kssj", defaultKssj);
        String jssj = getPara("jssj", defaultJssj);
        String ckqb = getPara("ckqb", "只显示有数据");
        setAttr("ckqb", ckqb);
        String nf = kssj.substring(0, 4);
        String ksyf = kssj.substring(0, 7);
        String jsyf = jssj.substring(0, 7);
        String tqkssj = plusYear(kssj, -1);
        String tqjssj = plusYear(jssj, -1);
        String tqksyf = tqkssj.substring(0, 7);
        String tqjsyf = tqjssj.substring(0, 7);
        String qnkssj = plusYear(kssj, -2);
        String qnjssj = plusYear(jssj, -2);
        String dqnkssj = plusYear(kssj, -3);
        String dqnjssj = plusYear(jssj, -3);
        setAttr("kssj", kssj);
        setAttr("jssj", jssj);
        Map<String, Map<String, String>> resultMap = Maps.newLinkedHashMap();
        List<Record> accountRecordList = Db.find("select * from account order by userName");
        Map<String, Record> accountMap = Maps.newHashMap();
        accountRecordList.forEach((record) -> accountMap.put(record.getStr("userName"), record));

        // 新增客户
        List<Record> khsRecord = Db.find("select hzywy,count(*) khs " +
                "from kh " +
                "where hzsj >= ? and hzsj <= ? and hzywy is not null and hzywy<>'' group by hzywy", kssj, jssj);
        khsRecord.forEach((r) -> {
            String ywy = getYwy(accountMap, r , "hzywy");
            Map<String, String> map = resultMap.getOrDefault(ywy, Maps.newHashMap());
            map.put("xzkhs", r.getStr("khs"));
            resultMap.put(ywy, map);
        });

        // 新增客户销售额(RMB) 新增客户毛利
        List<Record> xzRecord = Db.find(
                "select a.userName, concat(round(sum(ifnull(d.hkermb,0)),0)) sjddje, concat(round(sum((ifnull(d.ddje,0) + ifnull(d.dduyf, 0) + ifnull(d.sjdj, 0))* ifnull(l.xs, 0)), 0)) ddjermb, concat(round(sum(round(ifnull(d.ml, 0)+ifnull(d.yszkrmb, 0)-ifnull(v.dm_fj, 0)-ifnull(v.rm_fj, 0)-ifnull(v.zx_fj, 0)-ifnull(v.cl_fj, 0), 2)), 0)) ml "
                        +
                        "from dd d left join dd_kz v on d.ddbh=v.ddbh left join lrhl l on d.hb = l.hb and l.kssj <= d.cq and l.jssj >= d.cq, vw_mrtcxs_zhcf t, account a "
                        +
                        "where d.kh in (select id from kh where hzsj >= ? and hzsj <= ?) " +
                        "  and d.lx='正常订单' " +
                        "  and d.kh = t.khid and t.DAY_SHORT_DESC=d.cq " +
                        "  and t.ywyid = a.id " +
                        "  and d.cq >= ? " +
                        "  and d.cq <= ? " +
                        "group by a.userName;",
                kssj, jssj, kssj, jssj);
        xzRecord.forEach((r) -> {
            String ywy = getYwy(accountMap, r , "userName");
            Map<String, String> map = resultMap.getOrDefault(ywy, Maps.newHashMap());
            map.put("xzddjermb", r.getStr("ddjermb"));
            map.put("xzsjddje", r.getStr("sjddje"));
            map.put("xzml", r.getStr("ml"));
            resultMap.put(ywy, map);
        });

        // 总销售额(RMB) 总毛利
        List<Record> zRecord = Db.find(
                "select a.userName, concat(round(sum(ifnull(d.hkermb,0)),0)) sjddje, concat(round(sum((ifnull(d.ddje,0) + ifnull(d.dduyf, 0) + ifnull(d.sjdj, 0))* ifnull(l.xs, 0)), 0)) ddjermb, concat(round(sum(round(ifnull(d.ml, 0)+ifnull(d.yszkrmb, 0)-ifnull(v.dm_fj, 0)-ifnull(v.rm_fj, 0)-ifnull(v.zx_fj, 0)-ifnull(v.cl_fj, 0), 2)), 0)) ml, concat(round(sum(ifnull(d.spe, 0)*d.whkhl), 0)) spe, concat(round(sum(ifnull(d.spcz, 0)*d.whkhl), 0)) spcz, concat(round(sum(ifnull(d.tcsp, 0)*d.whkhl), 0)) tcsp "
                        +
                        "from dd d left join dd_kz v on d.ddbh=v.ddbh left join lrhl l on d.hb = l.hb and l.kssj <= d.cq and l.jssj >= d.cq, vw_mrtcxs_zhcf t, account a "
                        +
                        "where d.lx='正常订单' " +
                        "  and d.kh = t.khid and t.DAY_SHORT_DESC=d.cq " +
                        "  and t.ywyid = a.id " +
                        "  and d.cq >= ? " +
                        "  and d.cq <= ? " +
                        "group by a.userName;",
                kssj, jssj);
        zRecord.forEach((r) -> {
            String ywy = getYwy(accountMap, r , "userName");
            Map<String, String> map = resultMap.getOrDefault(ywy, Maps.newHashMap());
            map.put("zddjermb", r.getStr("ddjermb"));
            map.put("sjddje", r.getStr("sjddje"));
            map.put("zml", r.getStr("ml"));
            map.put("spe", r.getStr("spe"));
            map.put("spcz", r.getStr("spcz"));
            map.put("tcsp", r.getStr("tcsp"));
            map.put("zsp", d2s(s2d(r.getStr("spe")) + s2d(r.getStr("spcz")), 0));
            resultMap.put(ywy, map);
        });

        // 基本工资 提成
        List<Record> gzRecord = Db.find(
                "select a.userName, concat(round(avg(ifnull(gz, 0) + ifnull(gjj, 0) + ifnull(ysb, 0) + ifnull(gs, 0))*549, 0)) wz0,concat(round(sum(ifnull(zj, 0) - ifnull(ztc, 0)), 0)) jbgz, concat(round(sum(ifnull(ztc, 0)), 0)) ztc "
                        +
                        "from gz g, account a " +
                        "where g.yf >= ? " +
                        "  and g.yf <= ? and g.xm=a.xm " +
                        "group by a.userName",
                ksyf, jsyf);
        gzRecord.forEach((r) -> {
            String ywy = getYwy(accountMap, r , "userName");
            Map<String, String> map = resultMap.getOrDefault(ywy, Maps.newHashMap());
            map.put("jbgz", r.getStr("jbgz"));
            map.put("tc", r.getStr("ztc"));
            map.put("wz0", r.getStr("wz0"));
            resultMap.put(ywy, map);
        });

        // 基本工资 提成
        List<Record> tqgzRecord = Db.find(
                "select a.userName, concat(round(avg(ifnull(gz, 0) + ifnull(gjj, 0) + ifnull(ysb, 0) + ifnull(gs, 0))*549, 0)) wz0,concat(round(sum(ifnull(zj, 0) - ifnull(ztc, 0)), 0)) jbgz, concat(round(sum(ifnull(ztc, 0)), 0)) ztc "
                        +
                        "from gz g, account a " +
                        "where g.yf >= ? " +
                        "  and g.yf <= ? and g.xm=a.xm " +
                        "group by a.userName",
                tqksyf, tqjsyf);
        tqgzRecord.forEach((r) -> {
            String ywy = getYwy(accountMap, r , "userName");
            Map<String, String> map = resultMap.getOrDefault(ywy, Maps.newHashMap());
            map.put("tqjbgz", r.getStr("jbgz"));
            map.put("tqtc", r.getStr("ztc"));
            map.put("tqwz0", r.getStr("wz0"));
            resultMap.put(ywy, map);
        });

        // 同期总销售额(RMB) 总毛利
        List<Record> tqRecord = Db.find(
                "select a.userName, concat(round(sum(ifnull(d.hkermb,0)),0)) sjddje, concat(round(sum((ifnull(d.ddje,0) + ifnull(d.dduyf, 0) + ifnull(d.sjdj, 0))* ifnull(l.xs, 0)), 0)) ddjermb, concat(round(sum(round(ifnull(d.ml, 0)+ifnull(d.yszkrmb, 0)-ifnull(v.dm_fj, 0)-ifnull(v.rm_fj, 0)-ifnull(v.zx_fj, 0)-ifnull(v.cl_fj, 0), 2)), 0)) ml, concat(round(sum(ifnull(spe, 0)), 0), max(d.hb)) spe "
                        +
                        "from dd d left join dd_kz v on d.ddbh=v.ddbh left join lrhl l on d.hb = l.hb and l.kssj <= d.cq and l.jssj >= d.cq, vw_mrtcxs_zhcf t, account a "
                        +
                        "where d.lx='正常订单' " +
                        "  and d.kh = t.khid and t.DAY_SHORT_DESC=d.cq " +
                        "  and t.ywyid = a.id " +
                        "  and d.cq >= ? " +
                        "  and d.cq <= ? " +
                        "group by a.userName;",
                tqkssj, tqjssj);
        tqRecord.forEach((r) -> {
            String ywy = getYwy(accountMap, r , "userName");
            Map<String, String> map = resultMap.getOrDefault(ywy, Maps.newHashMap());
            map.put("tqddjermb", r.getStr("ddjermb"));
            map.put("tqsjddje", r.getStr("sjddje"));
            map.put("tqml", r.getStr("ml"));
            map.put("tqspe", r.getStr("spe"));
            resultMap.put(ywy, map);
        });

        // 前年总销售额(RMB) 总毛利
        List<Record> qnRecord = Db.find(
                "select a.userName, concat(round(sum(ifnull(d.hkermb,0)),0)) sjddje, concat(round(sum((ifnull(d.ddje,0) + ifnull(d.dduyf, 0) + ifnull(d.sjdj, 0))* ifnull(l.xs, 0)), 0)) ddjermb, concat(round(sum(round(ifnull(d.ml, 0)+ifnull(d.yszkrmb, 0)-ifnull(v.dm_fj, 0)-ifnull(v.rm_fj, 0)-ifnull(v.zx_fj, 0)-ifnull(v.cl_fj, 0), 2)), 0)) ml, concat(round(sum(ifnull(spe, 0)), 0), max(d.hb)) spe "
                        +
                        "from dd d left join dd_kz v on d.ddbh=v.ddbh left join lrhl l on d.hb = l.hb and l.kssj <= d.cq and l.jssj >= d.cq, vw_mrtcxs_zhcf t, account a "
                        +
                        "where d.lx='正常订单' " +
                        "  and d.kh = t.khid and t.DAY_SHORT_DESC=d.cq " +
                        "  and t.ywyid = a.id " +
                        "  and d.cq >= ? " +
                        "  and d.cq <= ? " +
                        "group by a.userName;",
                qnkssj, qnjssj);
        qnRecord.forEach((r) -> {
            String ywy = getYwy(accountMap, r , "userName");
            Map<String, String> map = resultMap.getOrDefault(ywy, Maps.newHashMap());
            map.put("qnddjermb", r.getStr("ddjermb"));
            map.put("qnsjddje", r.getStr("sjddje"));
            map.put("qnml", r.getStr("ml"));
            map.put("qnspe", r.getStr("spe"));
            resultMap.put(ywy, map);
        });

        // 大前年总销售额(RMB) 总毛利
        List<Record> dqnRecord = Db.find(
                "select a.userName, concat(round(sum(ifnull(d.hkermb,0)),0)) sjddje, concat(round(sum((ifnull(d.ddje,0) + ifnull(d.dduyf, 0) + ifnull(d.sjdj, 0))* ifnull(l.xs, 0)), 0)) ddjermb, concat(round(sum(round(ifnull(d.ml, 0)+ifnull(d.yszkrmb, 0)-ifnull(v.dm_fj, 0)-ifnull(v.rm_fj, 0)-ifnull(v.zx_fj, 0)-ifnull(v.cl_fj, 0), 2)), 0)) ml, concat(round(sum(ifnull(spe, 0)), 0), max(d.hb)) spe "
                        +
                        "from dd d left join dd_kz v on d.ddbh=v.ddbh left join lrhl l on d.hb = l.hb and l.kssj <= d.cq and l.jssj >= d.cq, vw_mrtcxs_zhcf t, account a "
                        +
                        "where d.lx='正常订单' " +
                        "  and d.kh = t.khid and t.DAY_SHORT_DESC=d.cq " +
                        "  and t.ywyid = a.id " +
                        "  and d.cq >= ? " +
                        "  and d.cq <= ? " +
                        "group by a.userName;",
                dqnkssj, dqnjssj);
        dqnRecord.forEach((r) -> {
            String ywy = getYwy(accountMap, r , "userName");
            Map<String, String> map = resultMap.getOrDefault(ywy, Maps.newHashMap());
            map.put("dqnddjermb", r.getStr("ddjermb"));
            map.put("dqnsjddje", r.getStr("sjddje"));
            map.put("dqnml", r.getStr("ml"));
            map.put("dqnspe", r.getStr("spe"));
            resultMap.put(ywy, map);
        });

        List<Record> ndmbRecord = Db.find(
                "select ywy, zt, concat(ifnull(mbe, 0)) mbe, concat(ifnull(xzmbe, 0)) xzmbe, concat(ifnull(z0, 0)) z0, concat(ifnull(m0, 0)) m0, concat(ifnull(m1, 0)) m1, concat(ifnull(m2, 0)) m2, concat(ifnull(m3, 0)) m3, concat(ifnull(m4, 0)) m4, concat(ifnull(zjx, 0)) zjx, "
                        +
                        " concat(ifnull(z0jx, 0)) z0jx,concat(ifnull(m0jx, 0)) m0jx, concat(ifnull(m1jx, 0)) m1jx, concat(ifnull(m2jx, 0)) m2jx, concat(ifnull(m3jx, 0)) m3jx, concat(ifnull(m4jx, 0)) m4jx, concat(ifnull(mbjx, 0)) mbjx "
                        +
                        " from ndmb " +
                        "where nf= ? ",
                nf);
        ndmbRecord.forEach((r) -> {
            String ywy = r.getStr("ywy");
            setAttr("zt", trueString(r.getStr("zt"), "0"));
            Map<String, String> map = resultMap.getOrDefault(ywy, Maps.newHashMap());
            map.put("mbe", r.getStr("mbe"));
            map.put("xzmbe", r.getStr("xzmbe"));
            map.put("Z0", r.getStr("z0"));
            map.put("Z0JX", r.getStr("z0jx"));
            map.put("M0", r.getStr("m0"));
            map.put("M1", r.getStr("m1"));
            map.put("M2", r.getStr("m2"));
            map.put("M3", r.getStr("m3"));
            map.put("M4", r.getStr("m4"));
            map.put("M0JX", r.getStr("m0jx"));
            map.put("M1JX", r.getStr("m1jx"));
            map.put("M2JX", r.getStr("m2jx"));
            map.put("M3JX", r.getStr("m3jx"));
            map.put("M4JX", r.getStr("m4jx"));
            map.put("ZJX", r.getStr("zjx"));
            map.put("MBJX", r.getStr("mbjx"));
            resultMap.put(ywy, map);
        });

        Map<String, String> columMap = getColumMap(tqkssj.substring(2, 4), qnkssj.substring(2, 4),
                dqnkssj.substring(2, 4));

        Set<String> keySet = Sets.newLinkedHashSet(resultMap.keySet());
        for (String key : keySet) {
            Map<String, String> map = resultMap.get(key);
            String zddjermb = map.get("sjddje");
            String tqddjermb = map.get("tqsjddje");
            String z0 = map.get("Z0");
            map.put("lmbce", d2s(s2d(zddjermb) - s2d(map.get("mbe")), 0));
            map.put("lxzce", d2s(s2d(map.get("xzddjermb")) - s2d(map.get("xzmbe")), 0));
            if (!StringUtils.isEmpty(zddjermb) && !StringUtils.isEmpty(tqddjermb)) {
                map.put("tqzz", d2s(100 * (s2d(zddjermb) - s2d(tqddjermb)) / s2d(tqddjermb)) + "%");
            }
            if (!StringUtils.isEmpty(zddjermb) && !StringUtils.isEmpty(z0)) {
                map.put("jszz", d2s(100 * (s2d(zddjermb) - s2d(z0)) / s2d(z0)) + "%");
            }
            resultMap.put(key, map);
        }
        for (String key : keySet) {
            for (String columKey : columMap.keySet()) {
                if ("tqzz".equals(columKey) || "jszz".equals(columKey)) {
                    continue;
                }
                Map<String, String> hjMap = resultMap.getOrDefault("合计", Maps.newHashMap());
                hjMap.put(columKey, d2s(s2d(resultMap.get(key).get(columKey)) + s2d(hjMap.get(columKey)), 0));
                resultMap.put("合计", hjMap);
            }
        }
        Map<String, String> hjMap = resultMap.getOrDefault("合计", Maps.newHashMap());
        hjMap.put("tqzz",
                d2s(100 * (s2d(hjMap.get("sjddje")) - s2d(hjMap.get("tqsjddje"))) / s2d(hjMap.get("tqsjddje"))) + "%");
        hjMap.put("jszz", d2s(100 * (s2d(hjMap.get("sjddje")) - s2d(hjMap.get("Z0"))) / s2d(hjMap.get("Z0"))) + "%");
        resultMap.put("合计", hjMap);

        setAttr("columMap", columMap);
        setAttr("resultMap", resultMap);
        set("nf", nf);
        render("ykph.html");
    }

    public void getJtxs() {
        String je = get("je");
        Record record = Db.findFirst("select * from jtxs where zx<=? and zd>=? and tm='2023石材'", je, je);
        renderJson(Ret.ok().set("record", JsonKit.toJson(record)));
    }

    public void closeAllData() {
        String nf = getPara("nf");
        Db.update("update ndmb set zt=1 where nf=?", nf);
        renderJson(Ret.ok(nf + " 的盈亏平衡数据锁定成功!"));
    }

    public void openAllData() {
        String nf = getPara("nf");
        Db.update("update ndmb set zt=0 where nf=?", nf);
        renderJson(Ret.ok(nf + " 的盈亏平衡数据解锁成功!"));
    }

    @Before(ManagerAuthInterceptor.class)
    public void show() {
        String col = get("col");
        String head = get("head");
        String nf = get("nf");
        String kssj = nf + "-01-01";
        String jssj = nf + "-12-31";
        Record account = Db.findFirst("select * from account where xm = ?", col);
        StringBuilder resultHtml = new StringBuilder();
        switch (head) {
            case "xzkhs": {
                List<Record> khsRecord = Db.find("select id,jc " +
                        "from kh " +
                        "where hzsj >= ? and hzsj <= ? and hzywy=? ", kssj, jssj, account.getStr("userName"));
                resultHtml = new StringBuilder(
                        "<table class=\"table table-bordered table-hover margin-top-10\" style=\"width: 98%\"><thead><tr><th>简称</th></tr></thead><tbody>");
                for (Record record : khsRecord) {
                    resultHtml.append("<tr><td><a href='/my/kh/edit?id=").append(record.getStr("id"))
                            .append("' target='_blank'>").append(record.getStr("jc")).append("</a></td></tr>");
                }
                resultHtml.append("</tbody></table>");
                break;
            }
            case "xzddjermb": {
                List<Record> xzRecord = Db.find(
                        "select d.id, d.ddbh, d.cq, concat(round(d.hkermb, 2)) ddjermb, concat(round(ifnull(d.ml, 0), 2)) ml "
                                +
                                "from dd d, vw_mrtcxs_zhcf t " +
                                "where d.kh in (select id from kh where hzsj >= ? and hzsj <= ?) " +
                                "  and d.lx='正常订单' " +
                                "  and d.kh = t.khid and t.DAY_SHORT_DESC=d.cq " +
                                "  and t.ywyid = ? " +
                                "  and d.cq >= ? " +
                                "  and d.cq <= ? ",
                        kssj, jssj, account.getInt("id"), kssj, jssj);
                resultHtml = new StringBuilder(
                        "<table class=\"table table-bordered table-hover margin-top-10\" style=\"width: 98%\"><thead><tr><th>订单编号</th><th>船期</th><th>订单金额(RMB)</th><th>毛利</th></tr></thead><tbody>");
                for (Record record : xzRecord) {
                    resultHtml.append("<tr><td><a href='/my/dd/edit?id=").append(record.getStr("id"))
                            .append("' target='_blank'>").append(record.getStr("ddbh")).append("</a></td><td>")
                            .append(record.getStr("cq")).append("</td><td>").append(record.getStr("ddjermb"))
                            .append("</td><td>").append(record.getStr("ml")).append("</td></tr>");
                }
                resultHtml.append("</tbody></table>");
                break;
            }
            case "zddjermb": {
                List<Record> zRecord = Db.find(
                        "select d.id, d.ddbh, d.cq, concat(round(d.hkermb, 2)) ddjermb, concat(round(ifnull(d.ml, 0), 2)) ml, concat(round(ifnull(d.spe, 0)*d.whkhl, 0)) spe, concat(round(ifnull(d.spcz, 0)*d.whkhl, 0)) spcz, concat(round(ifnull(d.tcsp, 0)*d.whkhl, 0)) tcsp "
                                +
                                "from dd d, vw_mrtcxs_zhcf t " +
                                "where d.lx='正常订单' " +
                                "  and d.kh = t.khid and t.DAY_SHORT_DESC=d.cq " +
                                "  and t.ywyid = ? " +
                                "  and d.cq >= ? " +
                                "  and d.cq <= ? order by d.ddbh,d.cq ",
                        account.getInt("id"), kssj, jssj);
                resultHtml = new StringBuilder(
                        "<table class=\"table table-bordered table-hover margin-top-10\" style=\"width: 98%\"><thead><tr><th>订单编号</th><th>船期</th><th>订单金额(RMB)</th><th>毛利</th><th>索赔金额</th><th>索赔重做</th><th>提成索赔</th></tr></thead><tbody>");
                for (Record record : zRecord) {
                    resultHtml.append("<tr><td><a href='/my/dd/edit?id=").append(record.getStr("id"))
                            .append("' target='_blank'>").append(record.getStr("ddbh")).append("</a></td><td>")
                            .append(record.getStr("cq")).append("</td><td>").append(record.getStr("ddjermb"))
                            .append("</td><td>").append(record.getStr("ml")).append("</td><td>")
                            .append(record.getStr("spe")).append("</td><td>").append(record.getStr("spcz"))
                            .append("</td><td>").append(record.getStr("tcsp")).append("</td></tr>");
                }
                resultHtml.append("</tbody></table>");
                break;
            }
            default:
                break;
        }
        renderHtml(resultHtml.toString());
    }

    public void gxndsymb() {
        String ywy = get("ywy");
        String nf = get("nf");
        String z0 = get("z0", "0");
        String m0 = get("m0", "0");
        String m1 = get("m1", "0");
        String m2 = get("m2", "0");
        String m3 = get("m3", "0");
        String m4 = get("m4", "0");
        String z0jx = get("z0jx", "0");
        String m0jx = get("m0jx", "0");
        String m1jx = get("m1jx", "0");
        String m2jx = get("m2jx", "0");
        String m3jx = get("m3jx", "0");
        String m4jx = get("m4jx", "0");
        String zjx = get("zjx", "0");
        String mbjx = get("mbjx", "0");
        if (StringUtils.isEmpty(ywy) || StringUtils.isEmpty(nf)) {
            renderJson("没有对应的业务员!");
            return;
        }
        Ndmb ndmb = ndmbService.findByYwyNf(ywy, nf);
        if (ndmb != null) {
            ndmb.set("z0", z0);
            ndmb.set("m0", m0);
            ndmb.set("m1", m1);
            ndmb.set("m2", m2);
            ndmb.set("m3", m3);
            ndmb.set("m4", m4);
            ndmb.set("z0jx", z0jx);
            ndmb.set("m0jx", m0jx);
            ndmb.set("m1jx", m1jx);
            ndmb.set("m2jx", m2jx);
            ndmb.set("m3jx", m3jx);
            ndmb.set("m4jx", m4jx);
            ndmb.set("zjx", zjx);
            ndmb.set("mbjx", mbjx);
            ndmb.update();
        } else {
            Ndmb newNdmb = new Ndmb();
            newNdmb.setYwy(ywy);
            newNdmb.setNf(nf);
            newNdmb.set("z0", z0);
            newNdmb.set("m0", m0);
            newNdmb.set("m1", m1);
            newNdmb.set("m2", m2);
            newNdmb.set("m3", m3);
            newNdmb.set("m4", m4);
            newNdmb.set("m0jx", m0jx);
            newNdmb.set("m1jx", m1jx);
            newNdmb.set("m2jx", m2jx);
            newNdmb.set("m3jx", m3jx);
            newNdmb.set("m4jx", m4jx);
            newNdmb.set("zjx", zjx);
            newNdmb.save();
        }
        NdmbZs ndmbZs = srv.findByYwyNf(ywy, nf);
        Integer id;
        if (ndmbZs != null) {
            ndmbZs.set("z0", z0);
            ndmbZs.set("m0", m0);
            ndmbZs.set("m1", m1);
            ndmbZs.set("m2", m2);
            ndmbZs.set("m3", m3);
            ndmbZs.set("m4", m4);
            ndmbZs.update();
            id = ndmbZs.getId();
        } else {
            NdmbZs newNdmb = new NdmbZs();
            newNdmb.setYwy(ywy);
            newNdmb.setNf(nf);
            newNdmb.set("z0", z0);
            newNdmb.set("m0", m0);
            newNdmb.set("m1", m1);
            newNdmb.set("m2", m2);
            newNdmb.set("m3", m3);
            newNdmb.set("m4", m4);
            newNdmb.save();
            id = newNdmb.getId();
        }
        renderJson(Ret.ok("更新成功!").set("id", id));
    }

    @Before(ManagerAuthInterceptor.class)
    public void gxndmb() {
        String ywy = get("ywy");
        String nf = get("nf");
        String value = get("value");
        if (StringUtils.isEmpty(ywy) || StringUtils.isEmpty(nf)) {
            renderJson("没有对应的业务员!");
            return;
        }
        Ndmb ndmb = ndmbService.findByYwyNf(ywy, nf);
        Integer id;
        if (ndmb != null) {
            ndmb.setMbe(value);
            ndmb.update();
            id = ndmb.getId();
        } else {
            Ndmb newNdmb = new Ndmb();
            newNdmb.setYwy(ywy);
            newNdmb.setNf(nf);
            newNdmb.setMbe(value);
            newNdmb.save();
            id = newNdmb.getId();
        }
        renderJson(Ret.ok("更新成功!").set("id", id));
    }

    @Before(ManagerAuthInterceptor.class)
    public void gxndxzmb() {
        String ywy = get("ywy");
        String nf = get("nf");
        String value = get("value");
        if (StringUtils.isEmpty(ywy) || StringUtils.isEmpty(nf)) {
            renderJson("没有对应的业务员!");
            return;
        }
        Ndmb ndmb = ndmbService.findByYwyNf(ywy, nf);
        Integer id;
        if (ndmb != null) {
            ndmb.setXzmbe(value);
            ndmb.update();
            id = ndmb.getId();
        } else {
            Ndmb newNdmb = new Ndmb();
            newNdmb.setYwy(ywy);
            newNdmb.setNf(nf);
            newNdmb.setXzmbe(value);
            newNdmb.save();
            id = newNdmb.getId();
        }
        renderJson(Ret.ok("更新成功!").set("id", id));
    }

    @Before(ManagerAuthInterceptor.class)
    public void gxndqtmb() {
        String ywy = get("ywy");
        String type = get("type");
        String nf = get("nf");
        String value = get("value");
        if (StringUtils.isEmpty(ywy) || StringUtils.isEmpty(nf) || StringUtils.isEmpty(type)) {
            renderJson("没有对应的业务员!");
            return;
        }
        Ndmb ndmb = ndmbService.findByYwyNf(ywy, nf);
        Integer id;
        if (ndmb != null) {
            ndmb.set(type.toLowerCase(), value);
            ndmb.update();
            id = ndmb.getId();
        } else {
            Ndmb newNdmb = new Ndmb();
            newNdmb.setYwy(ywy);
            newNdmb.setNf(nf);
            newNdmb.set(type.toLowerCase(), value);
            newNdmb.save();
            id = newNdmb.getId();
        }
        renderJson(Ret.ok("更新成功!").set("id", id));
    }

    @Before(ManagerAuthInterceptor.class)
    public void yfbb() {
        keepPara();
        DateTime dateTime = DateTime.now();
        String kssj = getPara("kssj", dateTime.toString("yyyy-01-01"));
        String jssj = getPara("jssj", dateTime.toString("yyyy-MM-dd"));
        String khid = getPara("khid");
        String ywy = getPara("ywy");
        String gb = getPara("gb");
        String mytk = getPara("mytk");
        String px = getPara("px");
        String ddbh = getPara("ddbh");
        set("kssj", kssj);
        set("jssj", jssj);
        set("px", px);
        setAttr("khList", Db.find("select id,jc,mytk from kh order by jc"));
        setAttr("mytkList", Db.find("select mytk from kh group by mytk order by mytk"));
        setAttr("ywyList", Db.find(
                "select distinct id,xm from account where status<>-1 and id in (select distinct accountId from account_role where roleId in (9, 11)) order by xm"));
        String sql = "select d.ddbh, " +
                "       d.cq, " +
                "       round(ifnull(d.cs, 0), 2) cs, " +
                "       h.mdg, " +
                "       if(p.zddbh is null, '否', '是') sfpg, " +
                "       k.ddujsjl, " +
                "       k.mytk, " +
                "       round(ifnull(d.yzf, 0), 2) yzf, " +
                "       round(ifnull(d.yzf, 0) / if(d.cs is null or abs(d.cs) = 0, 1, d.cs), 2) yzmc, " +
                "       round(ifnull(v.yf, 0), 2) yf, " +
                "       round(ifnull(v.yf, 0)*ifnull(v.yfhl, 1), 2) yfrmb, " +
                "       v.yfbz, " +
                "       v.yfhl, " +
                "       round(ifnull(v.yf, 0)*ifnull(v.yfhl, 1) / if(d.cs is null or abs(d.cs) = 0, 1, d.cs), 2)  yfmc, "
                +
                "       round(ifnull(v.gz, 0), 2) gz, " +
                "       round(ifnull(v.gz, 0)*ifnull(v.gzhl, 1), 2) gzrmb, " +
                "       v.gzbz, " +
                "       v.gzhl, " +
                "       round(ifnull(v.qg, 0), 2) qg, " +
                "       round(ifnull(v.qg, 0)*ifnull(v.qghl, 1), 2) qgrmb, " +
                "       v.qgbz, " +
                "       v.qghl, " +
                "       round(ifnull(v.sh, 0), 2) sh, " +
                "       round(ifnull(v.sh, 0)*ifnull(v.shhl, 1), 2) shrmb, " +
                "       v.shbz, " +
                "       v.shhl, " +
                "       round(ifnull(v.sj, 0), 2) sj, " +
                "       round(ifnull(v.sj, 0)*ifnull(v.sjhl, 1), 2) sjrmb, " +
                "       v.sjbz, " +
                "       v.sjhl, " +
                "       round(ifnull(v.hzyf, 0), 2) hzyf " +
                "from dd d " +
                "         left join dd_kz v on d.ddbh = v.ddbh " +
                "         left join dzhy h on d.ddbh = h.ddbh " +
                "         left join vw_dzhydp p on d.ddbh = p.ddbh " +
                "         left join kh k on d.kh = k.id " +
                "where d.lx <> '询价单' " +
                "  and d.cq >= '" + kssj + "' " +
                "  and d.cq <= '" + jssj + "' ";
        if (!StringUtils.isEmpty(ddbh)) {
            sql += " and d.ddbh like '%" + ddbh + "%' ";
        }
        if (!StringUtils.isEmpty(khid)) {
            sql += " and d.kh = " + khid + " ";
        }
        if (!StringUtils.isEmpty(ywy)) {
            sql += " and  d.kh in (select id from kh where ywy like concat('%' ,(select username from account where id="
                    + ywy + "), '%')) ";
        }
        if (!StringUtils.isEmpty(gb)) {
            sql += " and d.kh in (select id from kh where gb='" + gb + "') ";
        }
        if (!StringUtils.isEmpty(mytk)) {
            sql += " and d.kh in (select id from kh where mytk='" + mytk + "') ";
        }
        if ("船期逆序".equals(px)) {
            sql += " order by d.cq desc ";
        } else if ("订单编号正序".equals(px)) {
            sql += " order by d.ddbh ";
        } else if ("订单编号逆序".equals(px)) {
            sql += " order by d.ddbh desc ";
        } else {
            sql += " order by d.cq ";
        }
        setAttr("records", Db.find(sql));
        render("yfbb.html");
    }

    @Before(ManagerAuthInterceptor.class)
    public void ddgk() {
        keepPara();

        DateTime dateTime = DateTime.now();
        String kssj = getPara("kssj", dateTime.toString("yyyy-01-01"));
        String jssj = getPara("jssj", dateTime.toString("yyyy-MM-dd"));
        String khid = getPara("khid");
        String ywy = getPara("ywy");
        String gb = getPara("gb");
        String px = getPara("px");
        String mllStart = getPara("mllStart");
        String mllEnd = getPara("mllEnd");
        String csStart = getPara("csStart");
        String csEnd = getPara("csEnd");
        String ddbh = getPara("ddbh");
        set("kssj", kssj);
        set("jssj", jssj);
        set("px", px);
        setAttr("khList", Db.find("select id,jc from kh order by jc"));
        setAttr("ywyList", Db.find(
                "select distinct id,xm from account where status<>-1 and id in (select distinct accountId from account_role where roleId in (9, 11)) order by xm"));
        if (!StringUtils.isEmpty(kssj) && !StringUtils.isEmpty(jssj)) {
            setAttr("records", Db.find(Db.getSqlPara("bb.ddqk",
                    Kv.by("kssj", kssj)
                            .set("jssj", jssj)
                            .set("khid", khid)
                            .set("ywy", ywy)
                            .set("gb", gb)
                            .set("mllStart", mllStart)
                            .set("mllEnd", mllEnd)
                            .set("csStart", csStart)
                            .set("csEnd", csEnd)
                            .set("ddbh", ddbh)
                            .set("px", px))));
            setAttr("totalRecords", Db.find(Db.getSqlPara("bb.ddqk_hz",
                    Kv.by("kssj", kssj)
                            .set("jssj", jssj)
                            .set("khid", khid)
                            .set("khid", khid)
                            .set("ywy", ywy)
                            .set("gb", gb)
                            .set("mllStart", mllStart)
                            .set("mllEnd", mllEnd)
                            .set("csStart", csStart)
                            .set("csEnd", csEnd)
                            .set("ddbh", ddbh)
                            .set("px", px))));
        }
        render("ddgk.html");
    }

    @Before(ManagerAuthInterceptor.class)
    public void ddgkjb() {
        keepPara();

        DateTime dateTime = DateTime.now();
        String kssj = getPara("kssj", dateTime.toString("yyyy-01-01"));
        String jssj = getPara("jssj", dateTime.toString("yyyy-MM-dd"));
        String khid = getPara("khid");
        String ywy = getPara("ywy");
        String gb = getPara("gb");
        String px = getPara("px");
        String mllStart = getPara("mllStart");
        String mllEnd = getPara("mllEnd");
        String csStart = getPara("csStart");
        String csEnd = getPara("csEnd");
        String ddbh = getPara("ddbh");
        set("kssj", kssj);
        set("jssj", jssj);
        set("px", px);
        setAttr("khList", Db.find("select id,jc from kh order by jc"));
        setAttr("ywyList", Db.find(
                "select distinct id,xm from account where status<>-1 and id in (select distinct accountId from account_role where roleId in (9, 11)) order by xm"));
        if (!StringUtils.isEmpty(kssj) && !StringUtils.isEmpty(jssj)) {
            setAttr("records", Db.find(Db.getSqlPara("bb.ddqk",
                    Kv.by("kssj", kssj)
                            .set("jssj", jssj)
                            .set("khid", khid)
                            .set("ywy", ywy)
                            .set("gb", gb)
                            .set("mllStart", mllStart)
                            .set("mllEnd", mllEnd)
                            .set("csStart", csStart)
                            .set("csEnd", csEnd)
                            .set("ddbh", ddbh)
                            .set("px", px))));
            setAttr("totalRecords", Db.find(Db.getSqlPara("bb.ddqk_hz",
                    Kv.by("kssj", kssj)
                            .set("jssj", jssj)
                            .set("khid", khid)
                            .set("khid", khid)
                            .set("ywy", ywy)
                            .set("gb", gb)
                            .set("mllStart", mllStart)
                            .set("mllEnd", mllEnd)
                            .set("csStart", csStart)
                            .set("csEnd", csEnd)
                            .set("ddbh", ddbh)
                            .set("px", px))));
        }
        render("ddgkjb.html");
    }

    @Before(ManagerAuthInterceptor.class)
    public void ddgkrb() {
        keepPara();
        String rq = getPara("rq");
        String khid = getPara("khid");
        setAttr("khList", Db.find("select id,jc from kh order by jc"));
        if (!StringUtils.isEmpty(rq)) {
            setAttr("records", Db.find(Db.getSqlPara("bb.ddqkrb", Kv.by("rq", rq).set("khid", khid))));
            setAttr("totalRecords", Db.find(Db.getSqlPara("bb.ddqkrb_hz", Kv.by("rq", rq).set("khid", khid))));
        }
        render("ddgkrb.html");
    }

    @Before(ManagerAuthInterceptor.class)
    public void ddgkyb() {
        keepPara();
        String yf = getPara("yf");
        String khid = getPara("khid");
        setAttr("khList", Db.find("select id,jc from kh order by jc"));
        if (!StringUtils.isEmpty(yf)) {
            setAttr("records", Db.find(Db.getSqlPara("bb.ddqkyb", Kv.by("yf", yf).set("khid", khid))));
            setAttr("totalRecords", Db.find(Db.getSqlPara("bb.ddqkyb_hz", Kv.by("yf", yf).set("khid", khid))));
        }
        render("ddgkyb.html");
    }

    @Before(ManagerAuthInterceptor.class)
    public void wdsddgk() {
        keepPara();
        String kssj = getPara("kssj");
        String jssj = getPara("jssj");
        if (!StringUtils.isEmpty(kssj) && !StringUtils.isEmpty(jssj)) {
            setAttr("records", Db.find(Db.getSqlPara("bb.wdsddqk", Kv.by("kssj", kssj).set("jssj", jssj))));
            setAttr("totalRecords", Db.find(Db.getSqlPara("bb.wdsddqk_hz", Kv.by("kssj", kssj).set("jssj", jssj))));
        }
        render("wdsddgk.html");
    }

    @Before(ManagerAuthInterceptor.class)
    public void wjg() {
        keepPara();
        String kssj = getPara("kssj");
        String jssj = getPara("jssj");
        if (!StringUtils.isEmpty(kssj) && !StringUtils.isEmpty(jssj)) {
            List<Record> records = Db.find(Db.getSqlPara("bb.wjg", kssj, jssj));
            setAttr("records", records);
        }
        render("wjg.html");
    }

    @Before(ManagerAuthInterceptor.class)
    public void tc() {
        keepPara();
        String kssj = getPara("kssj", formatDate(getCurrYearFirst(), "yyyy-MM-dd"));
        String jssj = getPara("jssj", formatDate(new Date(), "yyyy-MM-dd"));
        setAttr("kssj", kssj);
        setAttr("jssj", jssj);
        List<Record> records = Db.find(Db.getSqlPara("bb.tc", kssj, jssj));
        setAttr("records", records);
        render("tc.html");
    }

    @Before(ManagerAuthInterceptor.class)
    public void yttc() {
        keepPara();
        String kssj = getPara("kssj", formatDate(getCurrYearFirst(), "yyyy-MM-dd"));
        String jssj = getPara("jssj", formatDate(new Date(), "yyyy-MM-dd"));
        Page<Record> page = Db.paginate(getParaToInt("p", 1), 500, Db.getSqlPara("bb.yttc", kssj, jssj));
        setAttr(kssj, kssj);
        setAttr(jssj, jssj);
        setAttr("page", page);
        render("yttc.html");
    }

    @Before(ManagerAuthInterceptor.class)
    public void lrnb() {
        keepPara();
        String nf = getPara("nf", formatDate(getCurrYearFirst(), "yyyy"));
        Record record = Db.findFirst(Db.getSqlPara("bb.lrnb", nf));

        setAttr("record", record);
        render("lrnb.html");
    }

    public void sclrnb() {
        String nf = getPara("nf", formatDate(getCurrYearFirst(), "yyyy"));
        Db.execute((connection) -> {
            try (CallableStatement cs = connection.prepareCall("{call sclrnb(?)}")) {
                cs.setString(1, nf);
                cs.execute();
                return cs.getString(2);
            }
        });
        renderJson(Ret.ok("生成成功!"));
    }

    @Before(ManagerAuthInterceptor.class)
    public void fy() {
        keepPara();
        String kssj = getPara("kssj");
        String jssj = getPara("jssj");
        String gssc = getPara("gssc");
        String fztj = getPara("fztj", "月份");
        if (StringUtils.isEmpty(kssj) || StringUtils.isEmpty(jssj)) {
            render("fy.html");
            return;
        }
        List<Record> records;
        if ("国家".equalsIgnoreCase(fztj)) {
            records = Db.find(Db.getSqlPara("bb.fy_gssc", kssj, jssj, gssc));
        } else {
            records = Db.find(Db.getSqlPara("bb.yf_yf", kssj, jssj, gssc));
        }
        LinkedHashSet<String> headSet = Sets.newLinkedHashSet();
        LinkedHashSet<String> colSet = Sets.newLinkedHashSet();
        Map<String, String> resultMap = Maps.newHashMap();
        for (Record record : records) {
            String fztjValue = record.getStr("fztj");
            String jtfy = record.getStr("jtfy");
            String je = record.getStr("je");
            headSet.add(jtfy);
            colSet.add(fztjValue);
            resultMap.put(fztjValue + "_" + jtfy, trueXlsString(je));
            if (resultMap.containsKey(jtfy)) {
                resultMap.put(jtfy, d2s(s2d(resultMap.get(jtfy)) + s2d(je)));
            } else {
                resultMap.put(jtfy, trueXlsString(je));
            }
        }
        setAttr("headSet", headSet);
        setAttr("colSet", colSet);
        setAttr("resultMap", resultMap);
        render("fy.html");
    }

    @Before(ManagerAuthInterceptor.class)
    public void ylglgz() throws ParseException {
        keepPara();
        String ksyf = getPara("ksyf", formatDate(getCurrYearFirst(), "yyyyMM"));
        String jsyf = getPara("jsyf", formatDate(new Date(), "yyyyMM"));
        List<String> monthBetween = getMonthBetween(ksyf, jsyf);
        monthBetween.add("合计");
        String gd = getPara("gd", "");
        List<Record> records;
        records = Db.find(
                "select ifnull(gd, '合计') gd, ifnull(yf, '合计') yf, sum(ifnull(sfgz,0)+ifnull(sfjj,0)) gz from ylglgz where yf>='"
                        + ksyf + "' and yf<='" + jsyf + "' and gd like '%" + gd + "%' group by gd,yf with rollup;");
        LinkedHashSet<String> colSet = Sets.newLinkedHashSet();
        Map<String, String> resultMap = Maps.newHashMap();
        for (Record record : records) {
            String fzgd = record.getStr("gd");
            String yf = record.getStr("yf");
            String gz = record.getStr("gz");
            colSet.add(fzgd);
            resultMap.put(fzgd + "_" + yf, trueXlsString(gz));
        }
        setAttr("headSet", monthBetween);
        setAttr("colSet", colSet);
        setAttr("resultMap", resultMap);
        render("ylglgz.html");
    }

    @Before(ManagerAuthInterceptor.class)
    public void ylgrgz() throws ParseException {
        keepPara();
        String ksyf = getPara("ksyf", formatDate(getCurrYearFirst(), "yyyyMM"));
        String jsyf = getPara("jsyf", formatDate(new Date(), "yyyyMM"));
        List<String> monthBetween = getMonthBetween(ksyf, jsyf);
        monthBetween.add("合计");
        String gzh = getPara("gzh", "");
        List<Record> records;
        records = Db
                .find("select ifnull(gzh, '合计') gzh, ifnull(yf, '合计') yf, sum(ifnull(sf,0)) gz from ylgrgz where yf>='"
                        + ksyf + "' and yf<='" + jsyf + "' and gzh like '%" + gzh + "%' group by gzh,yf with rollup;");
        LinkedHashSet<String> colSet = Sets.newLinkedHashSet();
        Map<String, String> resultMap = Maps.newHashMap();
        for (Record record : records) {
            String fzgzh = record.getStr("gzh");
            String yf = record.getStr("yf");
            String gz = record.getStr("gz");
            colSet.add(fzgzh);
            resultMap.put(fzgzh + "_" + yf, trueXlsString(gz));
        }
        setAttr("headSet", monthBetween);
        setAttr("colSet", colSet);
        setAttr("resultMap", resultMap);
        render("ylgrgz.html");
    }

    @Before(ManagerAuthInterceptor.class)
    public void ylbb() throws ParseException {
        keepPara();
        String ksyf = getPara("ksyf", formatDate(getCurrYearFirst(), "yyyyMM"));
        String jsyf = getPara("jsyf", formatDate(new Date(), "yyyyMM"));
        setAttr("ksyf", ksyf);
        setAttr("jsyf", jsyf);
        List<String> monthBetween = getMonthBetween(ksyf, jsyf);
        LinkedHashSet<String> colSet = Sets.newLinkedHashSet();
        colSet.add("月汇总");
        colSet.add("才数平均");
        colSet.add("利润");
        Map<String, String> resultMap = Maps.newHashMap();
        Map<String, Double> zchjMap = Maps.newHashMap();
        Map<String, Double> ggfMap = Maps.newHashMap();
        Set<String> ggfSet = Sets.newHashSet();
        Map<String, Double> zhjMap = Maps.newHashMap();
        Map<String, Double> lrhjMap = Maps.newHashMap();

        List<Record> alchRecords = Db
                .find("select replace(substr(cq, 1, 7), '-', '') yf, round(sum(ifnull(m.cs, 0)), 2) sz " +
                        "from ddmx m, " +
                        "     dd d " +
                        "where m.ddbh = d.ddbh and m.zwszm is not null " +
                        "  and (isnull(`m`.`gc`) or (`m`.`gc` = '')) " +
                        "  and (`d`.`lx` = '正常订单') " +
                        "  and replace(substr(cq, 1, 7), '-', '') >= '" + ksyf + "' " +
                        "  and replace(substr(cq, 1, 7), '-', '') <= '" + jsyf + "' " +
                        "group by replace(substr(cq, 1, 7), '-', '')");
        colSet.add("出货才数");
        for (Record record : alchRecords) {
            String yf = record.getStr("yf");
            String sz = trueXlsString(record.getStr("sz"));
            resultMap.put("出货才数" + "_" + yf, sz);
            String rowTotalKey = "出货才数_合计";
            if (resultMap.containsKey(rowTotalKey)) {
                resultMap.compute(rowTotalKey, (k, v) -> d2s(s2d(v) + s2d(sz)));
            } else {
                resultMap.put(rowTotalKey, sz);
            }
        }

        List<Record> alcbRecords = Db.find(
                "select replace(substr(d.cq, 1, 7), '-', '') yf, concat(round(sum(ifnull(b.cb, 0) + ifnull(d.wjg, 0)+ifnull(w.dm_fj, 0)+ifnull(w.rm_fj, 0)+ifnull(w.zx_fj, 0)+ifnull(w.cl_fj, 0)), 2)) sz "
                        +
                        "from dd d left join vw_clcb_bc b on d.ddbh=b.ddbh left join dd_kz w on d.ddbh=w.ddbh " +
                        "where d.lx='正常订单' " +
                        "  and replace(substr(d.cq, 1, 7), '-', '') >= '" + ksyf + "' " +
                        "  and replace(substr(d.cq, 1, 7), '-', '') <= '" + jsyf + "' " +
                        "group by replace(substr(d.cq, 1, 7), '-', '') ");
        colSet.add("销售额");
        for (Record record : alcbRecords) {
            String yf = record.getStr("yf");
            String sz = trueXlsString(record.getStr("sz"));
            resultMap.put("销售额" + "_" + yf, sz);
            String rowTotalKey = "销售额" + "_合计";
            lrhjMap.put(yf, lrhjMap.getOrDefault(yf, 0D) + s2d(sz));
            lrhjMap.put("合计", lrhjMap.getOrDefault("合计", 0D) + s2d(sz));
            if (resultMap.containsKey(rowTotalKey)) {
                resultMap.compute(rowTotalKey, (k, v) -> d2s(s2d(v) + s2d(sz)));
            } else {
                resultMap.put(rowTotalKey, sz);
            }
        }

        List<Record> byjRecords = Db.find("select yf, round(sum(kx),2) sz " +
                "from ylbyj " +
                "where yf >= '" + ksyf + "' " +
                "  and yf <= '" + jsyf + "' " +
                "group by yf");
        colSet.add("备用金");
        for (Record record : byjRecords) {
            String yf = record.getStr("yf");
            String sz = trueXlsString(record.getStr("sz"));
            resultMap.put("备用金" + "_" + yf, sz);
            zchjMap.put(yf, zchjMap.getOrDefault(yf, 0D) + s2d(sz));
            zchjMap.put("合计", zchjMap.getOrDefault("合计", 0D) + s2d(sz));
            ggfMap.put(yf, ggfMap.getOrDefault(yf, 0D) + s2d(sz));
            ggfMap.put("合计", ggfMap.getOrDefault("合计", 0D) + s2d(sz));
            ggfSet.add("备用金");
            String rowTotalKey = "备用金" + "_合计";
            if (resultMap.containsKey(rowTotalKey)) {
                resultMap.compute(rowTotalKey, (k, v) -> d2s(s2d(v) + s2d(sz)));
            } else {
                resultMap.put(rowTotalKey, sz);
            }
        }

        List<Record> glgzRecords = Db.find("select yf, round(sum(ifnull(sfgz,0)+ifnull(sfjj,0)),2) sz " +
                "from ylglgz " +
                "where yf >= '" + ksyf + "' " +
                "  and yf <= '" + jsyf + "' " +
                "group by yf");
        colSet.add("管理工资");
        for (Record record : glgzRecords) {
            String yf = record.getStr("yf");
            String sz = trueXlsString(record.getStr("sz"));
            resultMap.put("管理工资" + "_" + yf, sz);
            String rowTotalKey = "管理工资" + "_合计";
            zchjMap.put(yf, zchjMap.getOrDefault(yf, 0D) + s2d(sz));
            zchjMap.put("合计", zchjMap.getOrDefault("合计", 0D) + s2d(sz));
            ggfMap.put(yf, ggfMap.getOrDefault(yf, 0D) + s2d(sz));
            ggfMap.put("合计", ggfMap.getOrDefault("合计", 0D) + s2d(sz));
            ggfSet.add("管理工资");
            if (resultMap.containsKey(rowTotalKey)) {
                resultMap.compute(rowTotalKey, (k, v) -> d2s(s2d(v) + s2d(sz)));
            } else {
                resultMap.put(rowTotalKey, sz);
            }
        }

        List<Record> grgzRecords = Db.find("select gzh, yf, round(sum(sf),2) sz " +
                "from ylgrgz " +
                "where yf >= '" + ksyf + "' " +
                "  and yf <= '" + jsyf + "' " +
                "group by gzh, yf");
        for (Record record : grgzRecords) {
            String gzh = record.getStr("gzh");
            String yf = record.getStr("yf");
            String sz = trueXlsString(record.getStr("sz"));
            colSet.add(gzh);
            resultMap.put(gzh + "_" + yf, sz);
            String rowTotalKey = gzh + "_合计";
            zchjMap.put(yf, zchjMap.getOrDefault(yf, 0D) + s2d(sz));
            zchjMap.put("合计", zchjMap.getOrDefault("合计", 0D) + s2d(sz));
            ggfMap.put(yf, ggfMap.getOrDefault(yf, 0D) + s2d(sz));
            ggfMap.put("合计", ggfMap.getOrDefault("合计", 0D) + s2d(sz));
            ggfSet.add(gzh);
            if (resultMap.containsKey(rowTotalKey)) {
                resultMap.compute(rowTotalKey, (k, v) -> d2s(s2d(v) + s2d(sz)));
            } else {
                resultMap.put(rowTotalKey, sz);
            }
        }

        List<Record> wfRecords = Db.find("select y.mc, substr(y.yf, 1, 6) yf, round(sum(ifnull(y.je,0)),2) sz " +
                "from ylgzzf y " +
                "where substr(y.yf, 1, 6) >= '" + ksyf + "' " +
                "  and substr(y.yf, 1, 6) <= '" + jsyf + "' " +
                "group by y.mc, substr(y.yf, 1, 6)");
        for (Record record : wfRecords) {
            String xm = record.getStr("mc");
            String yf = record.getStr("yf");
            String sz = trueXlsString(record.getStr("sz"));
            colSet.add(xm);
            resultMap.put(xm + "_" + yf, sz);
            String rowTotalKey = xm + "_合计";
            zchjMap.put(yf, zchjMap.getOrDefault(yf, 0D) + s2d(sz));
            zchjMap.put("合计", zchjMap.getOrDefault("合计", 0D) + s2d(sz));
            ggfMap.put(yf, ggfMap.getOrDefault(yf, 0D) + s2d(sz));
            ggfMap.put("合计", ggfMap.getOrDefault("合计", 0D) + s2d(sz));
            ggfSet.add(xm);
            if (resultMap.containsKey(rowTotalKey)) {
                resultMap.compute(rowTotalKey, (k, v) -> d2s(s2d(v) + s2d(sz)));
            } else {
                resultMap.put(rowTotalKey, sz);
            }
        }

        List<Record> gcbxRecords = Db
                .find("select fylb, replace(substr(bxsj, 1, 7), '-', '') yf, round(sum(bxje),2) sz " +
                        "from gcbx " +
                        "where replace(substr(bxsj, 1, 7), '-', '') >= '" + ksyf + "' " +
                        "  and replace(substr(bxsj, 1, 7), '-', '') <= '" + jsyf + "' " +
                        "group by fylb, replace(substr(bxsj, 1, 7), '-', '')");
        for (Record record : gcbxRecords) {
            String fylb = record.getStr("fylb");
            String yf = record.getStr("yf");
            String sz = trueXlsString(record.getStr("sz"));
            colSet.add(fylb);
            resultMap.put(fylb + "_" + yf, sz);
            String rowTotalKey = fylb + "_合计";
            zchjMap.put(yf, zchjMap.getOrDefault(yf, 0D) + s2d(sz));
            zchjMap.put("合计", zchjMap.getOrDefault("合计", 0D) + s2d(sz));
            ggfMap.put(yf, ggfMap.getOrDefault(yf, 0D) + s2d(sz));
            ggfMap.put("合计", ggfMap.getOrDefault("合计", 0D) + s2d(sz));
            ggfSet.add(fylb);
            if (resultMap.containsKey(rowTotalKey)) {
                resultMap.compute(rowTotalKey, (k, v) -> d2s(s2d(v) + s2d(sz)));
            } else {
                resultMap.put(rowTotalKey, sz);
            }
        }

        List<Record> tsRecords = Db.find("select replace(substr(sj, 1, 7), '-', '') yf, round(sum(je), 2) sz " +
                "from ts " +
                "where replace(substr(sj, 1, 7), '-', '') >= '" + ksyf + "' " +
                "  and replace(substr(sj, 1, 7), '-', '') <= '" + jsyf + "' " +
                "group by replace(substr(sj, 1, 7), '-', '')");
        String cksb = "出口税补(澳林)";
        for (Record record : tsRecords) {
            String yf = record.getStr("yf");
            String sz = trueXlsString(record.getStr("sz"));
            resultMap.put(cksb + "_" + yf, sz);
            String rowTotalKey = cksb + "_合计";
            zhjMap.put(yf, zhjMap.getOrDefault(yf, 0D) - s2d(sz));
            zhjMap.put("合计", zhjMap.getOrDefault("合计", 0D) - s2d(sz));
            if (resultMap.containsKey(rowTotalKey)) {
                resultMap.compute(rowTotalKey, (k, v) -> d2s(s2d(v) + s2d(sz)));
            } else {
                resultMap.put(rowTotalKey, sz);
            }
        }

        List<Record> hlRecords = Db.find("select substr(gcdd, 1, 6) yf, round(sum(ifnull(mhzj, 0)), 2) sz " +
                "from ddmx " +
                "where ddbh like '荒料%' " +
                "  and substr(gcdd, 1, 6) >= '" + ksyf + "' " +
                "  and substr(gcdd, 1, 6) <= '" + jsyf + "' " +
                "group by substr(gcdd, 1, 6);");
        for (Record record : hlRecords) {
            String yf = record.getStr("yf");
            String sz = trueXlsString(record.getStr("sz"));
            String hlgm = "荒料购买(当年支出)";
            resultMap.put(hlgm + "_" + yf, sz);
            String rowTotalKey = hlgm + "_合计";
            zchjMap.put(yf, zchjMap.getOrDefault(yf, 0D) + s2d(sz));
            zchjMap.put("合计", zchjMap.getOrDefault("合计", 0D) + s2d(sz));
            if (resultMap.containsKey(rowTotalKey)) {
                resultMap.compute(rowTotalKey, (k, v) -> d2s(s2d(v) + s2d(sz)));
            } else {
                resultMap.put(rowTotalKey, sz);
            }
        }

        for (Map.Entry<String, Double> entry : zchjMap.entrySet()) {
            resultMap.put("营业成本合计(非成品)_" + entry.getKey(), d2s(entry.getValue()));
            // resultMap.put("出入合计_" + entry.getKey(), d2s(entry.getValue() +
            // zhjMap.getOrDefault(entry.getKey(), 0D)));
            resultMap.put("进出资金合计(非利润)_" + entry.getKey(), d2s(lrhjMap.getOrDefault(entry.getKey(), 0D)
                    - entry.getValue() - zhjMap.getOrDefault(entry.getKey(), 0D)));
        }
        for (Map.Entry<String, Double> entry : ggfMap.entrySet()) {
            resultMap.put("工+管+费合计_" + entry.getKey(), d2s(entry.getValue()));
        }
        resultMap.put("费用/才_合计",
                d2s(s2d(resultMap.getOrDefault("工+管+费合计_合计", "0")) / s2d(resultMap.getOrDefault("出货才数_合计", "1"))));
        String cfzj = "厂房折旧≈租赁费";
        colSet.add(cfzj);
        for (String yf : monthBetween) {
            String sz = "75000";
            String rowTotalKey = cfzj + "_合计";
            if (resultMap.containsKey(rowTotalKey)) {
                String v = resultMap.get(rowTotalKey);
                double zzj = s2d(v) + s2d(sz);
                if (zzj <= 9000000D) {
                    resultMap.put(cfzj + "_" + yf, sz);
                    ggfMap.put(yf, ggfMap.getOrDefault(yf, 0D) + s2d(sz));
                    ggfMap.put("合计", ggfMap.getOrDefault("合计", 0D) + s2d(sz));
                    ggfSet.add(cfzj);
                    resultMap.put(rowTotalKey, d2s(zzj));
                } else {
                    resultMap.put(cfzj + "_" + yf, "0");
                    resultMap.put(rowTotalKey, "9000000D");
                }
            } else {
                resultMap.put(cfzj + "_" + yf, sz);
                ggfMap.put(yf, ggfMap.getOrDefault(yf, 0D) + s2d(sz));
                ggfMap.put("合计", ggfMap.getOrDefault("合计", 0D) + s2d(sz));
                ggfSet.add(cfzj);
                resultMap.put(rowTotalKey, sz);
            }
        }
        colSet.add("工+管+费合计");
        colSet.add("荒料购买(当年支出)");
        colSet.add(cksb);
        colSet.add("营业成本合计(非成品)");
        // colSet.add("出入合计");
        colSet.add("进出资金合计(非利润)");
        colSet.add("费用/才");

        Map<String, String> resultMapCopy = Maps.newHashMap();
        resultMapCopy.putAll(resultMap);

        for (Map.Entry<String, String> entry : resultMapCopy.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            if (StringUtils.endsWith(key, "_合计")) {
                String[] s = key.split("_");
                if (ggfSet.contains(s[0])) {
                    String ggfhj = resultMap.getOrDefault("工+管+费合计_合计", "0");
                    if ("0".equalsIgnoreCase(ggfhj)) {
                        resultMap.put(s[0] + "_占比(工管费)", "--");
                    } else {
                        if (cksb.equalsIgnoreCase(s[0])) {
                            resultMap.put(s[0] + "_占比(工管费)", d2s(-s2d(value) * 100 / s2d(ggfhj), 2) + "%");
                        } else {
                            resultMap.put(s[0] + "_占比(工管费)", d2s(s2d(value) * 100 / s2d(ggfhj), 2) + "%");
                        }
                    }
                } else {
                    resultMap.put(s[0] + "_占比(工管费)", "--");
                }
            }
        }

        monthBetween.add("合计");
        monthBetween.add("占比(工管费)");
        setAttr("headSet", monthBetween);
        setAttr("colSet", colSet);
        setAttr("resultMap", resultMap);
        render("ylbb.html");
    }

    @Before(ManagerAuthInterceptor.class)
    public void ylbyj() {
        keepPara();
        String ksyf = getPara("ksyf", formatDate(getCurrYearFirst(), "yyyyMM"));
        String jsyf = getPara("jsyf", formatDate(new Date(), "yyyyMM"));
        List<Record> records = Db.find(
                "select ifnull(yf, '合计') yf, sum(ifnull(je,0)) je, sum(ifnull(kx,0)) kx, sum(ifnull(ye,0)) ye from ylbyj where yf>='"
                        + ksyf + "' and yf<='" + jsyf + "' group by yf with rollup;");
        setAttr("records", records);
        render("ylbyj.html");
    }

    @Before(ManagerAuthInterceptor.class)
    public void clcs() {
        keepPara();
        String kssj = getPara("kssj", formatDate(getCurrYearFirst(), "yyyy-MM-dd"));
        String jssj = getPara("jssj", formatDate(new Date(), "yyyy-MM-dd"));
        setAttr("kssj", kssj);
        setAttr("jssj", jssj);
        List<Record> records = Db.find(
                "select ifnull(zwszm, '') zwszm, avg(ifnull(price, 0)) price20, round(sum(ifnull(cs, 0)), 3) cs, convert(round(sum(ifnull(cb, 0)), 3), char) cb "
                        +
                        "from vw_szcstj " +
                        "where cq >= ? " +
                        "  and cq <= ? " +
                        "group by ifnull(zwszm, '') " +
                        "order by ifnull(zwszm, '')",
                kssj, jssj);
        List<Record> sumRecords = Db.find(
                "select '合计' zwszm, avg(ifnull(price, 0)) price20, round(sum(ifnull(cs, 0)), 3) cs, convert(round(sum(ifnull(cb, 0)), 3), char) cb "
                        +
                        "      from vw_szcstj " +
                        " where cq >= ? " +
                        "  and cq <= ? ",
                kssj, jssj);
        setAttr("records", records);
        setAttr("sumRecords", sumRecords);
        render("clcs.html");
    }

    @Before(ManagerAuthInterceptor.class)
    public void ddszcs() {
        keepPara();
        String ddbh = get("queryDdbh", "");
        String szmc = get("querySzmc", "");
        String kssj = getPara("kssj", formatDate(getCurrYearFirst(), "yyyy-MM-dd"));
        String jssj = getPara("jssj", formatDate(new Date(), "yyyy-MM-dd"));
        setAttr("kssj", kssj);
        setAttr("jssj", jssj);
        setAttr("queryDdbh", ddbh);
        setAttr("querySzmc", szmc);
        List<Record> records = Db.find(
                "select m.ddbh,m.zwszm,round(sum(ifnull(m.cs, 0)), 2) cs from ddmx m, dd d where m.ddbh=d.ddbh " +
                        "and d.lx<>'询价单' and d.cq>=? and d.cq<=? and m.zwszm is not null and m.ddbh like '%" + ddbh + "%' and m.zwszm like '%" + szmc + "%' " +
                        "group by m.ddbh,m.zwszm;",
                kssj, jssj);
        List<Record> sumRecords = Db.find(
                "select '合计' ddbh,'' zwszm,round(sum(ifnull(m.cs, 0)), 2) cs from ddmx m, dd d where m.ddbh=d.ddbh " +
                        "and d.lx<>'询价单' and d.cq>=? and d.cq<=? and m.zwszm is not null and m.ddbh like '%" + ddbh + "%' and m.zwszm like '%" + szmc + "%';",
                kssj, jssj);
        setAttr("records", records);
        setAttr("sumRecords", sumRecords);
        render("ddszcs.html");
    }

    @Before(ManagerAuthInterceptor.class)
    public void yzfyb() {
        keepPara();
        String ksyf = getPara("ksyf", formatDate(getCurrYearFirst(), "yyyy-MM"));
        String jsyf = getPara("jsyf", formatDate(new Date(), "yyyy-MM"));
        List<Record> records = Db.find(
                "select substring(cq, 1, 7) cq, round(sum(ifnull(yzf,0)),2) yzf from dd where substring(cq, 1, 7)>='"
                        + ksyf + "' and substring(cq, 1, 7)<='" + jsyf + "' group by substring(cq, 1, 7) with rollup;");
        setAttr("records", records);
        render("yzfyb.html");
    }

    @Before(ManagerAuthInterceptor.class)
    public void yzfmytkyb() {
        keepPara();
        String ksyf = getPara("ksyf", formatDate(getCurrYearFirst(), "yyyy-MM"));
        String jsyf = getPara("jsyf", formatDate(new Date(), "yyyy-MM"));
        List<Record> records = Db
                .find("select ifnull(cq, '合计') cq, ifnull(mytk, '合计') mytk, round(sum(ifnull(yzf, 0)), 2) yzf " +
                        "from (select substring(d.cq, 1, 7) cq, k.mytk mytk, ifnull(d.yzf, 0) yzf " +
                        "      from dd d, " +
                        "           kh k " +
                        "      where d.kh = k.id " +
                        "        and yzf is not null " +
                        "        and substring(cq, 1, 7) >= '" + ksyf + "' " +
                        "        and substring(cq, 1, 7) <= '" + jsyf + "') a " +
                        "group by cq, mytk " +
                        "with rollup;");
        setAttr("records", records);
        render("yzfmytkyb.html");
    }

    public void xj() {
        String ddbh = get("ddbh");
        String dqrq = DateTime.now().toString("yyyy-MM-dd");
        setAttr("hdList", Db.find("select mc, lxr, yx from hd order by mc"));
        setAttr("ddList", Db.find(
                "select distinct d.ddbh ddbh from dzhy v, dd d where v.ddbh=d.ddbh and d.lx='正常订单' and v.jyms<>'FOB' and (v.cfrq is null or v.cfrq>=?) order by d.ddbh",
                dqrq));
        setAttr("ddbh", ddbh);
        render("xj.html");
    }

    public void fsxj() {
        String hd = getPara("hd");
        String dd = getPara("dd");
        if (StringUtils.isEmpty(hd) || StringUtils.isEmpty(dd)) {
            renderJson(Ret.fail().set("msg", "没有选择货代或者没有选择订单!"));
        }
        List<String> hdList = new Gson().fromJson(hd, new TypeToken<List<String>>() {
        }.getType());
        List<String> ddList = new Gson().fromJson(dd, new TypeToken<List<String>>() {
        }.getType());
        for (String hdxx : hdList) {
            for (String ddbh : ddList) {
                TipHelperKit.sendXjEmail(ddbh, hdxx);
            }
        }
        renderJson(Ret.ok().set("msg", "发送成功!"));
    }

    public void lr() {
        DateTime dateTime = DateTime.now();
        String kssj = getPara("kssj", dateTime.toString("yyyy-01-01"));
        String jssj = getPara("jssj", dateTime.toString("yyyy-MM-dd"));

        setAttr("kssj", kssj);
        setAttr("jssj", jssj);

        String lastKssj = plusYear(kssj, -1);
        String lastJssj = plusYear(jssj, -1);
        Integer id = (Integer) Db.execute((connection) -> {
            try (CallableStatement cs = connection.prepareCall("{call sclrsb(?,?,?,?)}")) {
                cs.setString(1, kssj);
                cs.setString(2, jssj);
                cs.setInt(3, getLoginAccountId());
                cs.registerOutParameter(4, java.sql.Types.INTEGER);
                cs.execute();
                return cs.getInt(4);
            }
        });
        Integer lastId = (Integer) Db.execute((connection) -> {
            try (CallableStatement cs = connection.prepareCall("{call sclrsb(?,?,?,?)}")) {
                cs.setString(1, lastKssj);
                cs.setString(2, lastJssj);
                cs.setInt(3, getLoginAccountId());
                cs.registerOutParameter(4, java.sql.Types.INTEGER);
                cs.execute();
                return cs.getInt(4);
            }
        });
        Record lr = Db.findFirst("select * from lr where id=?", id);
        Record lastLr = Db.findFirst("select * from lr where id=?", lastId);
        List<Record> qtsrList = Db.find(
                "select jtfy, round(sum(round(ifnull(lastQtsr, 0), 2)), 2) lastQtsr, round(sum(round(ifnull(qtsr, 0), 2)), 2) qtsr "
                        +
                        "from (select jtfy, 0 lastQtsr, round(sum(round(0 - ifnull(bxje, 0), 2)), 2) qtsr " +
                        "      from bx " +
                        "      where fylb = '其他收入' " +
                        "        and bxsj between ? and ? " +
                        "      group by jtfy " +
                        "      union all " +
                        "      select jtfy, round(sum(round(0 - ifnull(bxje, 0), 2)), 2) lastQtsr, 0 qtsr " +
                        "      from bx " +
                        "      where fylb = '其他收入' " +
                        "        and bxsj between ? and ? " +
                        "      group by jtfy) aa " +
                        "group by jtfy order by jtfy;",
                kssj, jssj, lastKssj, lastJssj);
        setAttr("lr", lr);
        setAttr("lastLr", lastLr);
        setAttr("qtsrList", qtsrList);
        render("lr.html");
    }

    public void yxjc() {
        render("yxjc.html");
    }

    public void yxjc1() {
        UploadFile file = getFile();
        StringBuilder result = new StringBuilder();
        try {
            String content = FileUtils.readFileToString(file.getFile(), "UTF-8");
            getEmails(result, content);
        } catch (IOException e) {
            e.printStackTrace();
        }
        renderJson(Ret.ok().set("msg", "检测成功!").set("emails", result.toString()));
    }

    private void getEmails(StringBuilder result, String content) {
        List<String> emails = parseEmails(content);
        List<Record> records = Db.find("select distinct yx " +
                "from (select yx yx " +
                "      from kh " +
                "      union " +
                "      select khdylxr yx " +
                "      from kh " +
                "      union " +
                "      select khdelxr yx " +
                "      from kh " +
                "      union " +
                "      select khdslxr yx " +
                "      from kh " +
                "      union " +
                "      select khd4lxr yx " +
                "      from kh " +
                "      union " +
                "      select khd5lxr yx " +
                "      from kh " +
                "      union " +
                "      select khd6lxr yx " +
                "      from kh " +
                "      union " +
                "      select khd7lxr yx " +
                "      from kh " +
                "      union " +
                "      select khd8lxr yx " +
                "      from kh) a " +
                "where yx is not null " +
                "order by yx;");
        List<String> khEmails = Lists.newArrayList();
        for (Record record : records) {
            khEmails.add(record.getStr("yx"));
        }
        for (String email : emails) {
            if (khEmails.contains(email)) {
                result.append(email).append("\n");
            }
        }
    }

    public void yxjc2() {
        String yxnr = getPara("yxnr");
        StringBuilder result = new StringBuilder();
        getEmails(result, yxnr);
        renderJson(Ret.ok().set("msg", "检测成功!").set("emails", result.toString()));
    }

    public void yjmbhs() {
        DateTime dateTime = DateTime.now();
        String defaultKssj = dateTime.toString("yyyy-01-01");
        String defaultJssj = dateTime.toString("yyyy-12-31");
        if (dateTime.toString("yyyy-MM-dd").compareTo(dateTime.toString("yyyy-03-01")) < 0) {
            defaultKssj = plusYear(defaultKssj, -1);
            defaultJssj = plusYear(defaultJssj, -1);
        }
        String kssj = getPara("kssj", defaultKssj);
        String jssj = getPara("jssj", defaultJssj);
        String nf = kssj.substring(0, 4);
        String ksyf = kssj.substring(0, 7);
        String jsyf = jssj.substring(0, 7);
        String tqkssj = plusYear(kssj, -1);
        String tqjssj = plusYear(jssj, -1);
        setAttr("kssj", kssj);
        setAttr("jssj", jssj);
        Map<String, Map<String, String>> resultMap = Maps.newLinkedHashMap();
        List<Record> accountRecordList = Db.find("select * from account order by userName");
        Map<String, Record> accountMap = Maps.newHashMap();
        accountRecordList.forEach((record) -> accountMap.put(record.getStr("userName"), record));

        // 新增客户
        List<Record> khsRecord = Db.find("select hzywy,count(*) khs " +
                "from kh " +
                "where hzsj >= ? and hzsj <= ? group by hzywy", kssj, jssj);
        khsRecord.forEach((r) -> {
            String ywy = accountMap.get(r.getStr("hzywy")).getStr("xm");
            Map<String, String> map = resultMap.getOrDefault(ywy, Maps.newHashMap());
            map.put("xzkhs", r.getStr("khs"));
            resultMap.put(ywy, map);
        });

        // 新增客户销售额(RMB) 新增客户毛利
        List<Record> xzRecord = Db.find(
                "select a.userName, concat(round(sum(ifnull(d.hkermb,0)),0)) sjddje, concat(round(sum((ifnull(d.ddje,0) + ifnull(d.dduyf, 0) + ifnull(d.sjdj, 0))* ifnull(l.xs, 0)), 0)) ddjermb, concat(round(sum(round(ifnull(d.ml, 0)+ifnull(d.yszkrmb, 0)-ifnull(v.dm_fj, 0)-ifnull(v.rm_fj, 0)-ifnull(v.zx_fj, 0)-ifnull(v.cl_fj, 0), 2)), 0)) ml "
                        +
                        "from dd d left join dd_kz v on d.ddbh=v.ddbh left join lrhl l on d.hb = l.hb and l.kssj <= d.cq and l.jssj >= d.cq, vw_mrtcxs_zhcf t, account a "
                        +
                        "where d.kh in (select id from kh where hzsj >= ? and hzsj <= ?) " +
                        "  and d.lx='正常订单' " +
                        "  and d.kh = t.khid and t.DAY_SHORT_DESC=d.cq " +
                        "  and t.ywyid = a.id " +
                        "  and d.cq >= ? " +
                        "  and d.cq <= ? " +
                        "group by a.userName;",
                kssj, jssj, kssj, jssj);
        xzRecord.forEach((r) -> {
            String ywy = accountMap.get(r.getStr("userName")).getStr("xm");
            Map<String, String> map = resultMap.getOrDefault(ywy, Maps.newHashMap());
            map.put("xzddjermb", r.getStr("ddjermb"));
            map.put("xzsjddje", r.getStr("sjddje"));
            map.put("xzml", r.getStr("ml"));
            resultMap.put(ywy, map);
        });

        // 总销售额(RMB) 总毛利
        List<Record> zRecord = Db.find(
                "select a.userName, concat(round(sum(ifnull(d.hkermb,0)),0)) sjddje, concat(round(sum((ifnull(d.ddje,0) + ifnull(d.dduyf, 0) + ifnull(d.sjdj, 0))* ifnull(l.xs, 0)), 0)) ddjermb, concat(round(sum(round(ifnull(d.ml, 0)+ifnull(d.yszkrmb, 0)-ifnull(v.dm_fj, 0)-ifnull(v.rm_fj, 0)-ifnull(v.zx_fj, 0)-ifnull(v.cl_fj, 0), 2)), 0)) ml, concat(round(sum(ifnull(d.spe, 0)*d.whkhl), 0)) spe, concat(round(sum(ifnull(d.spcz, 0)*d.whkhl), 0)) spcz, concat(round(sum(ifnull(d.tcsp, 0)*d.whkhl), 0)) tcsp "
                        +
                        "from dd d left join dd_kz v on d.ddbh=v.ddbh left join lrhl l on d.hb = l.hb and l.kssj <= d.cq and l.jssj >= d.cq, vw_mrtcxs_zhcf t, account a "
                        +
                        "where d.lx='正常订单' " +
                        "  and d.kh = t.khid and t.DAY_SHORT_DESC=d.cq " +
                        "  and t.ywyid = a.id " +
                        "  and d.cq >= ? " +
                        "  and d.cq <= ? " +
                        "group by a.userName;",
                kssj, jssj);
        zRecord.forEach((r) -> {
            String ywy = accountMap.get(r.getStr("userName")).getStr("xm");
            Map<String, String> map = resultMap.getOrDefault(ywy, Maps.newHashMap());
            map.put("zddjermb", r.getStr("ddjermb"));
            map.put("sjddje", r.getStr("sjddje"));
            map.put("zml", r.getStr("ml"));
            map.put("spe", r.getStr("spe"));
            map.put("spcz", r.getStr("spcz"));
            map.put("tcsp", r.getStr("tcsp"));
            resultMap.put(ywy, map);
        });

        // 基本工资 提成
        List<Record> gzRecord = Db.find(
                "select a.userName, concat(round(sum(ifnull(zj, 0) -ifnull(ztc, 0)), 0)) jbgz, concat(round(sum(ifnull(ztc, 0)), 0)) ztc "
                        +
                        "from gz g, account a " +
                        "where g.yf >= ? " +
                        "  and g.yf <= ? and g.xm=a.xm " +
                        "group by a.userName",
                ksyf, jsyf);
        gzRecord.forEach((r) -> {
            String ywy = accountMap.get(r.getStr("userName")).getStr("xm");
            Map<String, String> map = resultMap.getOrDefault(ywy, Maps.newHashMap());
            map.put("jbgz", r.getStr("jbgz"));
            map.put("tc", r.getStr("ztc"));
            resultMap.put(ywy, map);
        });

        // 同期总销售额(RMB) 总毛利
        List<Record> tqRecord = Db.find(
                "select a.userName, concat(round(sum(ifnull(d.hkermb,0)),0)) sjddje, concat(round(sum((ifnull(d.ddje,0) + ifnull(d.dduyf, 0) + ifnull(d.sjdj, 0))* ifnull(l.xs, 0)), 0)) ddjermb, concat(round(sum(round(ifnull(d.ml, 0)+ifnull(d.yszkrmb, 0)-ifnull(v.dm_fj, 0)-ifnull(v.rm_fj, 0)-ifnull(v.zx_fj, 0)-ifnull(v.cl_fj, 0), 2)), 0)) ml, concat(round(sum(ifnull(d.spe, 0)), 0), max(d.hb)) spe "
                        +
                        "from dd d left join dd_kz v on d.ddbh=v.ddbh left join lrhl l on d.hb = l.hb and l.kssj <= d.cq and l.jssj >= d.cq, vw_mrtcxs_zhcf t, account a "
                        +
                        "where d.lx='正常订单' " +
                        "  and d.kh = t.khid and t.DAY_SHORT_DESC=d.cq " +
                        "  and t.ywyid = a.id " +
                        "  and d.cq >= ? " +
                        "  and d.cq <= ? " +
                        "group by a.userName;",
                tqkssj, tqjssj);
        tqRecord.forEach((r) -> {
            String ywy = accountMap.get(r.getStr("userName")).getStr("xm");
            Map<String, String> map = resultMap.getOrDefault(ywy, Maps.newHashMap());
            map.put("tqddjermb", r.getStr("ddjermb"));
            map.put("tqsjddje", r.getStr("sjddje"));
            map.put("tqml", r.getStr("ml"));
            map.put("tqspe", r.getStr("spe"));
            resultMap.put(ywy, map);
        });

        List<Record> ndmbRecord = Db.find(
                " select ywy, concat(ifnull(mbe, 0)) mbe, concat(ifnull(xzmbe, 0)) xzmbe, concat(ifnull(z0, 0)) z0, concat(ifnull(m0, 0)) m0, concat(ifnull(m1, 0)) m1, concat(ifnull(m2, 0)) m2, concat(ifnull(m3, 0)) m3, concat(ifnull(m4, 0)) m4, concat(ifnull(zjx, 0)) zjx, "
                        +
                        " concat(ifnull(z0jx, 0)) z0jx,concat(ifnull(m0jx, 0)) m0jx, concat(ifnull(m1jx, 0)) m1jx, concat(ifnull(m2jx, 0)) m2jx, concat(ifnull(m3jx, 0)) m3jx, concat(ifnull(m4jx, 0)) m4jx, concat(ifnull(mbjx, 0)) mbjx "
                        +
                        " from ndmb " +
                        " where nf= ? ",
                nf);
        ndmbRecord.forEach((r) -> {
            String ywy = r.getStr("ywy");
            Map<String, String> map = resultMap.getOrDefault(ywy, Maps.newHashMap());
            map.put("mbe", r.getStr("mbe"));
            map.put("xzmbe", r.getStr("xzmbe"));
            map.put("Z0", r.getStr("z0"));
            map.put("Z0JX", r.getStr("z0jx"));
            map.put("M0", r.getStr("m0"));
            map.put("M1", r.getStr("m1"));
            map.put("M2", r.getStr("m2"));
            map.put("M3", r.getStr("m3"));
            map.put("M4", r.getStr("m4"));
            map.put("M0JX", r.getStr("m0jx"));
            map.put("M1JX", r.getStr("m1jx"));
            map.put("M2JX", r.getStr("m2jx"));
            map.put("M3JX", r.getStr("m3jx"));
            map.put("M4JX", r.getStr("m4jx"));
            map.put("ZJX", r.getStr("zjx"));
            resultMap.put(ywy, map);
        });

        Map<String, String> columMap = Maps.newLinkedHashMap();
        // columMap.put("mbe", "年度总目标");
        // columMap.put("xzmbe", "新增目标");
        // columMap.put("Z0", "盈亏平衡点Z0");
        // columMap.put("M0", "保证目标M0");
        // columMap.put("M1", "进步目标M1");
        // columMap.put("M2", "优秀目标M2");
        // columMap.put("M3", "超级目标M3");
        // columMap.put("M4", "卓越目标M4");
        // columMap.put("xzkhs", "新增客户数");
        // columMap.put("xzddjermb", "新增客户的订单金额(RMB)");
        // columMap.put("xzml", "新增客户的毛利");
        // columMap.put("zddjermb", "总订单金额(RMB)");
        // columMap.put("lmbce", "离保障目标差额");
        // columMap.put("lxzce", "离新增目标差额");
        // columMap.put("zml", "总毛利");
        // columMap.put("spe", "金额索赔");
        // columMap.put("spcz", "重做索赔");
        // columMap.put("tcsp", "提成索赔");
        // columMap.put("jbgz", "基本工资");
        // columMap.put("tc", "提成");
        // columMap.put("tqddjermb", "上年度同期订单金额(RMB)");
        // columMap.put("tqml", "上年度同期毛利");
        // columMap.put("tqspe", "上年度同期索赔");

        Set<String> keySet = Sets.newLinkedHashSet(resultMap.keySet());
        for (String key : keySet) {
            Map<String, String> map = resultMap.get(key);
            map.put("lmbce", d2s(s2d(map.get("zddjermb")) - s2d(map.get("mbe")), 0));
            map.put("lxzce", d2s(s2d(map.get("xzddjermb")) - s2d(map.get("xzmbe")), 0));
            resultMap.put(key, map);
        }

        setAttr("columMap", columMap);
        setAttr("resultMap", resultMap);
        set("nf", nf);
        render("yjmbhs.html");
    }

    public void xsetj() {
        String ksrq = get("ksrq", formatDate(getCurrYearFirst(), "yyyy-MM-dd"));
        String jsrq = get("jsrq", yyyy_MM_dd());
        setAttr("ksrq", ksrq);
        setAttr("jsrq", jsrq);

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // 解析开始日期和结束日期
        LocalDate startDate = LocalDate.parse(ksrq, formatter);
        LocalDate endDate = LocalDate.parse(jsrq, formatter);

        // 计算前一年的同期日期
        LocalDate lastYearSameDayStart = startDate.minusYears(1);
        LocalDate lastYearSameDayEnd = endDate.minusYears(1);

        // 计算前一年的第一天和最后一天
        LocalDate firstDayOfLastYear = LocalDate.of(startDate.getYear() - 1, 1, 1);
        LocalDate lastDayOfLastYear = LocalDate.of(startDate.getYear() - 1, 12, 31);

        // 格式化结果为 YYYY-MM-DD
        String formattedLastYearSameDayStart = lastYearSameDayStart.format(formatter);
        String formattedLastYearSameDayEnd = lastYearSameDayEnd.format(formatter);
        String formattedFirstDayOfLastYear = firstDayOfLastYear.format(formatter);
        String formattedLastDayOfLastYear = lastDayOfLastYear.format(formatter);
        String dnzje = Db.queryStr(
                "select round(sum(ifnull(ddjermb, 0)),2) zje from dd where cq>=? and cq<=? and lx<>'询价单';", ksrq, jsrq);
        List<Record> records = Db.find("SELECT  " +
                        "    t1.khid, " +
                        "    t1.jc, " +
                        "    ROUND(t1.zje, 2) as zje, " +
                        "    ROUND(t2.tbzje, 2) as tbzje, " +
                        "    ROUND(t3.qnzje, 2) as qnzje, " +
                        "    CASE  " +
                        "        WHEN t2.tbzje = 0 OR t2.tbzje IS NULL THEN NULL " +
                        "        ELSE ROUND(t1.zje / t2.tbzje * 100, 2) " +
                        "    END as tb_ratio, " +
                        "    CASE  " +
                        "        WHEN t3.qnzje = 0 OR t3.qnzje IS NULL THEN NULL " +
                        "        ELSE ROUND(t1.zje / t3.qnzje * 100, 2) " +
                        "    END as hb_ratio, round(t1.zje*100/" + dnzje + ", 2) dnzb " +
                        "FROM ( " +
                        "    SELECT k.id khid, k.jc jc, SUM(IFNULL(ddjermb,0)) zje " +
                        "    FROM dd d " +
                        "    LEFT JOIN kh k ON d.kh = k.id " +
                        "    WHERE d.cq >= ? AND d.cq <= ? and d.lx<>'询价单' " +
                        "    GROUP BY k.id, k.jc " +
                        ") t1 " +
                        "LEFT JOIN ( " +
                        "    SELECT k.id khid, k.jc jc, SUM(IFNULL(ddjermb,0)) tbzje " +
                        "    FROM dd d " +
                        "    LEFT JOIN kh k ON d.kh = k.id " +
                        "    WHERE d.cq >= ? AND d.cq <= ? and d.lx<>'询价单' " +
                        "    GROUP BY k.id, k.jc " +
                        ") t2 ON t1.khid = t2.khid " +
                        "LEFT JOIN ( " +
                        "    SELECT k.id khid, k.jc jc, SUM(IFNULL(ddjermb,0)) qnzje " +
                        "    FROM dd d " +
                        "    LEFT JOIN kh k ON d.kh = k.id " +
                        "    WHERE d.cq >= ? AND d.cq <= ? and d.lx<>'询价单' " +
                        "    GROUP BY k.id, k.jc " +
                        ") t3 ON t1.khid = t3.khid order by ROUND(t1.zje, 2) desc;", ksrq, jsrq, formattedLastYearSameDayStart,
                formattedLastYearSameDayEnd, formattedFirstDayOfLastYear, formattedLastDayOfLastYear);
        set("records", records);
        render("xsetj.html");
    }

    public void khlrtj() {
        {
            String ksrq = get("ksrq", formatDate(getCurrYearFirst(), "yyyy-MM-dd"));
            String jsrq = get("jsrq", yyyy_MM_dd());
            setAttr("ksrq", ksrq);
            setAttr("jsrq", jsrq);

            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            // 解析开始日期和结束日期
            LocalDate startDate = LocalDate.parse(ksrq, formatter);
            LocalDate endDate = LocalDate.parse(jsrq, formatter);

            // 计算前一年的同期日期
            LocalDate lastYearSameDayStart = startDate.minusYears(1);
            LocalDate lastYearSameDayEnd = endDate.minusYears(1);

            // 计算前一年的第一天和最后一天
            LocalDate firstDayOfLastYear = LocalDate.of(startDate.getYear() - 1, 1, 1);
            LocalDate lastDayOfLastYear = LocalDate.of(startDate.getYear() - 1, 12, 31);

            // 格式化结果为 YYYY-MM-DD
            String formattedLastYearSameDayStart = lastYearSameDayStart.format(formatter);
            String formattedLastYearSameDayEnd = lastYearSameDayEnd.format(formatter);
            String formattedFirstDayOfLastYear = firstDayOfLastYear.format(formatter);
            String formattedLastDayOfLastYear = lastDayOfLastYear.format(formatter);
            String dnzml = Db.queryStr(
                    "select round(sum(round(ifnull(d.ml, 0)+ifnull(d.yszkrmb, 0)-ifnull(v.dm_fj, 0)-ifnull(v.rm_fj, 0)-ifnull(v.zx_fj, 0)-ifnull(v.cl_fj, 0), 2)), 2) ml from dd d left join dd_kz v on d.ddbh=v.ddbh where d.cq>=? and d.cq<=? and d.lx<>'询价单';",
                    ksrq, jsrq);
            List<Record> records = Db.find("SELECT  " +
                            "    t1.khid, " +
                            "    t1.jc, " +
                            "    ROUND(t1.zml, 2) as zml, " +
                            "    ROUND(t2.tbzml, 2) as tbzml, " +
                            "    ROUND(t3.qnzml, 2) as qnzml, " +
                            "    CASE  " +
                            "        WHEN t2.tbzml = 0 OR t2.tbzml IS NULL THEN NULL " +
                            "        ELSE ROUND(t1.zml / t2.tbzml * 100, 2) " +
                            "    END as tb_ratio, " +
                            "    CASE  " +
                            "        WHEN t3.qnzml = 0 OR t3.qnzml IS NULL THEN NULL " +
                            "        ELSE ROUND(t1.zml / t3.qnzml * 100, 2) " +
                            "    END as hb_ratio, round(t1.zml*100/" + dnzml + ", 2) dnzb " +
                            "FROM ( " +
                            "    SELECT k.id khid, k.jc jc, round(sum(round(ifnull(d.ml, 0)+ifnull(d.yszkrmb, 0)-ifnull(v.dm_fj, 0)-ifnull(v.rm_fj, 0)-ifnull(v.zx_fj, 0)-ifnull(v.cl_fj, 0), 2)), 2) zml "
                            +
                            "    FROM dd d " +
                            "    LEFT JOIN kh k ON d.kh = k.id " +
                            "    LEFT JOIN dd_kz v ON d.ddbh = v.ddbh " +
                            "    WHERE d.cq >= ? AND d.cq <= ? and d.lx<>'询价单' " +
                            "    GROUP BY k.id, k.jc " +
                            ") t1 " +
                            "LEFT JOIN ( " +
                            "    SELECT k.id khid, k.jc jc, round(sum(round(ifnull(d.ml, 0)+ifnull(d.yszkrmb, 0)-ifnull(v.dm_fj, 0)-ifnull(v.rm_fj, 0)-ifnull(v.zx_fj, 0)-ifnull(v.cl_fj, 0), 2)), 2) tbzml "
                            +
                            "    FROM dd d " +
                            "    LEFT JOIN kh k ON d.kh = k.id " +
                            "    LEFT JOIN dd_kz v ON d.ddbh = v.ddbh " +
                            "    WHERE d.cq >= ? AND d.cq <= ? and d.lx<>'询价单' " +
                            "    GROUP BY k.id, k.jc " +
                            ") t2 ON t1.khid = t2.khid " +
                            "LEFT JOIN ( " +
                            "    SELECT k.id khid, k.jc jc, round(sum(round(ifnull(d.ml, 0)+ifnull(d.yszkrmb, 0)-ifnull(v.dm_fj, 0)-ifnull(v.rm_fj, 0)-ifnull(v.zx_fj, 0)-ifnull(v.cl_fj, 0), 2)), 2) qnzml "
                            +
                            "    FROM dd d " +
                            "    LEFT JOIN kh k ON d.kh = k.id " +
                            "    LEFT JOIN dd_kz v ON d.ddbh = v.ddbh " +
                            "    WHERE d.cq >= ? AND d.cq <= ? and d.lx<>'询价单' " +
                            "    GROUP BY k.id, k.jc " +
                            ") t3 ON t1.khid = t3.khid order by ROUND(t1.zml, 2) desc;", ksrq, jsrq,
                    formattedLastYearSameDayStart, formattedLastYearSameDayEnd, formattedFirstDayOfLastYear,
                    formattedLastDayOfLastYear);
            set("records", records);
            render("khlrtj.html");
        }

    }

    public void synchronizeWjgData() {
        Db.update("CALL gx_wjg_fj()");
        Db.update("CALL gxddgk()");
        renderJson(Ret.ok("同步成功!"));
    }

    public void khtj() {
        String ksrq = get("ksrq", formatDate(getCurrYearFirst(), "yyyy-MM-dd"));
        String jsrq = get("jsrq", yyyy_MM_dd());
        String khid = get("khid");

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // 解析开始日期和结束日期
        LocalDate startDate = LocalDate.parse(ksrq, formatter);
        LocalDate endDate = LocalDate.parse(jsrq, formatter);

        // 验证日期范围
        LocalDate minDate = LocalDate.of(2020, 1, 1);
        if (startDate.isBefore(minDate)) {
            throw new IllegalArgumentException("开始日期不能早于2020年");
        }
        if (!startDate.isBefore(endDate)) {
            throw new IllegalArgumentException("开始日期必须早于结束日期");
        }

        // 计算所有同期日期范围
        List<Map<String, String>> periodRanges = new ArrayList<>();
        List<Record> detailList = new ArrayList<>();
        Map<String, Map<String, Object>> customerData = new HashMap<>();
        Set<String> timeRangeSet = Sets.newLinkedHashSet();

        LocalDate currentStartDate = startDate;
        LocalDate currentEndDate = endDate;

        int yearCount = 0;
        List<String> khList = Db.query("select jc from kh where id in (select kh from dd where lx<>'询价单' and lrsj>='2020-01-01') order by jc");

        while ((currentStartDate.isAfter(minDate) || currentStartDate.equals(minDate)) && yearCount < 6) {
            yearCount++;
            Map<String, String> period = new HashMap<>();
            String periodStart = currentStartDate.format(formatter);
            String periodEnd = currentEndDate.format(formatter);
            period.put("startDate", periodStart);
            period.put("endDate", periodEnd);
            periodRanges.add(period);

            // 构建SQL查询条件
            StringBuilder sql = new StringBuilder(
                    "select k.jc, round(sum(f.gcs), 2) gcs, round(sum(f.zcs), 2) zcs, round(sum(f.zje), 2) zje "
                            + "from vw_ddmx_fz f "
                            + "left join dd d on f.ddbh = d.ddbh "
                            + "left join kh k on d.kh = k.id "
                            + "where d.lrsj >= ? and d.lrsj <= ? ");

            List<Object> params = new ArrayList<>();
            params.add(periodStart);
            params.add(periodEnd);

            String timeRange = "\"" + periodStart + " 至 " + periodEnd + "\"";
            timeRangeSet.add(timeRange);
            if (khid != null && !"[]".equalsIgnoreCase(khid)) {
                sql.append("and d.kh in (").append(khid).append(") ");
            }

            sql.append(" group by k.jc order by k.jc");


            List<Record> periodData = Db.find(sql.toString(), params.toArray());

            for (Record record : periodData) {
                record.set("date", timeRange);
                detailList.add(record);

                // 按客户分组统计数据
                String customerName = record.getStr("jc");
                if (!customerData.containsKey(customerName)) {
                    customerData.put(customerName, new HashMap<String, Object>() {
                        {
                            put("jc", customerName);
                            put("timeList", new ArrayList<String>());
                            put("xseList", new ArrayList<BigDecimal>());
                            put("zcsList", new ArrayList<Integer>());
                            put("gcsList", new ArrayList<Integer>());
                        }
                    });
                }

                Map<String, Object> customer = customerData.get(customerName);
                List<String> timeList = (List<String>) customer.get("timeList");
                if (!timeList.contains(timeRange)) {
                    timeList.add(timeRange);
                    ((List<BigDecimal>) customer.get("xseList")).add(record.getBigDecimal("zje"));
                    ((List<Integer>) customer.get("zcsList")).add(record.getInt("zcs"));
                    ((List<Integer>) customer.get("gcsList")).add(record.getInt("gcs"));
                }
            }
            currentStartDate = currentStartDate.minusYears(1);
            currentEndDate = currentEndDate.minusYears(1);
        }
        for (String jc : khList) {
            for (String timeRange : timeRangeSet) {
                if (customerData.containsKey(jc)) {
                    Map<String, Object> map = customerData.get(jc);
                    List<String> timeList = (List<String>) map.get("timeList");
                    if (!timeList.contains(timeRange)) {
                        timeList.add(timeRange);
                        ((List<BigDecimal>) map.get("xseList")).add(new BigDecimal(0));
                        ((List<Integer>) map.get("zcsList")).add(0);
                        ((List<Integer>) map.get("gcsList")).add(0);
                    }
                }
            }
        }

        setAttr("ksrq", ksrq);
        setAttr("jsrq", jsrq);
        setAttr("periodRanges", periodRanges);
        setAttr("detailList", detailList);
        List<Map<String, Object>> sortedDataList = new ArrayList<>(customerData.values());
        // 对每个客户的时间段数据进行排序
        for (Map<String, Object> cData : sortedDataList) {
            List<String> timeList = (List<String>) cData.get("timeList");
            List<BigDecimal> xseList = (List<BigDecimal>) cData.get("xseList");
            List<Integer> zcsList = (List<Integer>) cData.get("zcsList");
            List<Integer> gcsList = (List<Integer>) cData.get("gcsList");

            // 创建索引列表用于排序
            List<Integer> indices = new ArrayList<>();
            for (int i = 0; i < timeList.size(); i++) {
                indices.add(i);
            }

            // 根据时间段进行排序
            indices.sort((i1, i2) -> {
                String time1 = timeList.get(i1).replaceAll("\"", "");
                String time2 = timeList.get(i2).replaceAll("\"", "");
                return time1.compareTo(time2);
            });

            // 根据排序后的索引重新排列数据
            List<String> sortedTimeList = new ArrayList<>();
            List<BigDecimal> sortedXseList = new ArrayList<>();
            List<Integer> sortedZcsList = new ArrayList<>();
            List<Integer> sortedGcsList = new ArrayList<>();

            for (int index : indices) {
                sortedTimeList.add(timeList.get(index));
                sortedXseList.add(xseList.get(index));
                sortedZcsList.add(zcsList.get(index));
                sortedGcsList.add(gcsList.get(index));
            }

            // 更新原始数据
            cData.put("timeList", sortedTimeList);
            cData.put("xseList", sortedXseList);
            cData.put("zcsList", sortedZcsList);
            cData.put("gcsList", sortedGcsList);
        }
        sortedDataList.sort(Comparator.comparing(a -> ((String) a.get("jc"))));

        setAttr("dataList", sortedDataList);
        if (khid != null) {
            setAttr("khid", JsonKit.toJson(khid.split(",")));
        } else {
            setAttr("khid", "[]");
        }
        setAttr("khSelectJson", getKhSelectJson());

        render("khtj.html");
    }

    public void sykhtj() {
        String ksrq = get("ksrq", formatDate(getCurrYearFirst(), "yyyy-MM-dd"));
        String jsrq = get("jsrq", yyyy_MM_dd());
        String khid = get("khid");

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // 解析开始日期和结束日期
        LocalDate startDate = LocalDate.parse(ksrq, formatter);
        LocalDate endDate = LocalDate.parse(jsrq, formatter);

        // 验证日期范围
        LocalDate minDate = LocalDate.of(2020, 1, 1);
        if (startDate.isBefore(minDate)) {
            throw new IllegalArgumentException("开始日期不能早于2020年");
        }
        if (!startDate.isBefore(endDate)) {
            throw new IllegalArgumentException("开始日期必须早于结束日期");
        }

        // 计算所有同期日期范围
        List<Map<String, String>> periodRanges = new ArrayList<>();
        List<Record> detailList = new ArrayList<>();
        Map<String, Map<String, Object>> customerData = new HashMap<>();

        LocalDate currentStartDate = startDate;
        LocalDate currentEndDate = endDate;

        int yearCount = 0;
        while ((currentStartDate.isAfter(minDate) || currentStartDate.equals(minDate)) && yearCount < 6) {
            yearCount++;
            Map<String, String> period = new HashMap<>();
            String periodStart = currentStartDate.format(formatter);
            String periodEnd = currentEndDate.format(formatter);
            period.put("startDate", periodStart);
            period.put("endDate", periodEnd);
            periodRanges.add(period);

            // 构建SQL查询条件
            StringBuilder sql = new StringBuilder(
                    "select d.hb,round(sum(f.gcs), 2) gcs, round(sum(f.zcs), 2) zcs, round(sum(f.zje), 2) zje "
                            + "from vw_ddmx_fz f "
                            + "left join dd d on f.ddbh = d.ddbh "
                            + "left join kh k on d.kh = k.id "
                            + "where d.lrsj >= ? and d.lrsj <= ? ");

            List<Object> params = new ArrayList<>();
            params.add(periodStart);
            params.add(periodEnd);

            if (khid != null && !"[]".equalsIgnoreCase(khid)) {
                sql.append("and d.kh in (").append(khid).append(") ");
            }
            sql.append("order by d.hb order by d.hb");

            List<Record> periodData = Db.find(sql.toString(), params.toArray());
            for (Record record : periodData) {
                record.set("date", "\"" + periodStart + " 至 " + periodEnd + "\"");
                detailList.add(record);

                // 按客户分组统计数据
                String customerName = record.getStr("hb");
                if (!customerData.containsKey(customerName)) {
                    customerData.put(customerName, new HashMap<String, Object>() {
                        {
                            put("jc", customerName);
                            put("timeList", new ArrayList<String>());
                            put("xseList", new ArrayList<BigDecimal>());
                            put("zcsList", new ArrayList<Integer>());
                            put("gcsList", new ArrayList<Integer>());
                        }
                    });
                }

                Map<String, Object> customer = customerData.get(customerName);
                String timeRange = "\"" + periodStart + " 至 " + periodEnd + "\"";
                List<String> timeList = (List<String>) customer.get("timeList");
                if (!timeList.contains(timeRange)) {
                    timeList.add(timeRange);
                    ((List<BigDecimal>) customer.get("xseList")).add(record.getBigDecimal("zje"));
                    ((List<Integer>) customer.get("zcsList")).add(record.getInt("zcs"));
                    ((List<Integer>) customer.get("gcsList")).add(record.getInt("gcs"));
                }
            }

            currentStartDate = currentStartDate.minusYears(1);
            currentEndDate = currentEndDate.minusYears(1);
        }

        setAttr("ksrq", ksrq);
        setAttr("jsrq", jsrq);
        setAttr("periodRanges", periodRanges);
        setAttr("detailList", detailList);
        List<Map<String, Object>> sortedDataList = new ArrayList<>(customerData.values());
        // 对每个客户的时间段数据进行排序
        for (Map<String, Object> cData : sortedDataList) {
            List<String> timeList = (List<String>) cData.get("timeList");
            List<BigDecimal> xseList = (List<BigDecimal>) cData.get("xseList");
            List<Integer> zcsList = (List<Integer>) cData.get("zcsList");
            List<Integer> gcsList = (List<Integer>) cData.get("gcsList");

            // 创建索引列表用于排序
            List<Integer> indices = new ArrayList<>();
            for (int i = 0; i < timeList.size(); i++) {
                indices.add(i);
            }

            // 根据时间段进行排序
            indices.sort((i1, i2) -> {
                String time1 = timeList.get(i1).replaceAll("\"", "");
                String time2 = timeList.get(i2).replaceAll("\"", "");
                return time1.compareTo(time2);
            });

            // 根据排序后的索引重新排列数据
            List<String> sortedTimeList = new ArrayList<>();
            List<BigDecimal> sortedXseList = new ArrayList<>();
            List<Integer> sortedZcsList = new ArrayList<>();
            List<Integer> sortedGcsList = new ArrayList<>();

            for (int index : indices) {
                sortedTimeList.add(timeList.get(index));
                sortedXseList.add(xseList.get(index));
                sortedZcsList.add(zcsList.get(index));
                sortedGcsList.add(gcsList.get(index));
            }

            // 更新原始数据
            cData.put("timeList", sortedTimeList);
            cData.put("xseList", sortedXseList);
            cData.put("zcsList", sortedZcsList);
            cData.put("gcsList", sortedGcsList);
        }
        sortedDataList.sort(Comparator.comparing(a -> ((String) a.get("jc"))));
        setAttr("dataList", sortedDataList);
        if (khid != null) {
            setAttr("khid", JsonKit.toJson(khid.split(",")));
        } else {
            setAttr("khid", "[]");
        }
        setAttr("khSelectJson", getKhSelectJson());

        render("sykhtj.html");
    }

    private String getKhSelectJson() {
        List<Record> gbList = Db.find("select id,jc from kh order by jc");
        XmSelect[] xmSelects = new XmSelect[gbList.size()];
        for (int i = 0; i < gbList.size(); i++) {
            xmSelects[i] = new XmSelect(gbList.get(i).getStr("jc"), gbList.get(i).getStr("id"));
        }
        return new Gson().toJson(xmSelects);
    }
}
