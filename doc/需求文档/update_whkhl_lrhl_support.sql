-- 更新视图以支持 lrhl 表作为 whkhl 表的备用数据源
-- 当 whkhl 表中没有对应的汇率系数时，从 lrhl 表中获取
-- 如果 lrhl 表中也没有，则使用默认值 1

-- 更新 vw_dd_whkhl 视图
create or replace view vw_dd_whkhl as
select ddbh, d.hb, d.cq, if(d.whkhl = '0' or d.whkhl is null, ifnull(w.xs, ifnull(l.xs, 1)), d.whkhl) whkhl
from dd d
         left join whkhl w on d.cq >= w.kssj and d.cq <= w.jssj and d.hb = w.hb
         left join lrhl l on d.cq >= l.kssj and d.cq <= l.jssj and d.hb = l.hb
where ((d.cq is not null and d.cq <> '' and (d.whkhl is null or d.whkhl = '0'))
    or (d.whkhl is not null and d.whkhl <> '0'))
  and d.hb is not null
  and d.hb <> ''
group by d.ddbh, d.hb, d.cq, d.whkhl;

-- 更新 vw_dd_hkhl 视图
create or replace view vw_dd_hkhl as
select m.jkid                                                                      ddbh,
       if(m.whkhl = '', ifnull(w.xs, ifnull(l.xs, '')), m.whkhl)                                  whkhl,
       if(m.sjjkhl = '', if(m.whkhl = '', ifnull(w.xs, ifnull(l.xs, 1)), m.whkhl), m.sjjkhl)     sjhkhl,
       if(m.sjjkhl1 = '', if(m.whkhl = '', ifnull(w.xs, ifnull(l.xs, 1)), m.whkhl), m.sjjkhl1)   sjhkhl1,
       if(m.sjjkhl2 = '', if(m.whkhl = '', ifnull(w.xs, ifnull(l.xs, 1)), m.whkhl), m.sjjkhl2)   sjhkhl2,
       if(m.sjjkhl3 = '', if(m.whkhl = '', ifnull(w.xs, ifnull(l.xs, 1)), m.whkhl), m.sjjkhl3)   sjhkhl3,
       if(m.sjjkhl4 = '', if(m.whkhl = '', ifnull(w.xs, ifnull(l.xs, 1)), m.whkhl), m.sjjkhl4)   sjhkhl4,
       if(m.sjjkhl5 = '', if(m.whkhl = '', ifnull(w.xs, ifnull(l.xs, 1)), m.whkhl), m.sjjkhl5)   sjhkhl5,
       if(m.sjjkhl6 = '', if(m.whkhl = '', ifnull(w.xs, ifnull(l.xs, 1)), m.whkhl), m.sjjkhl6)   sjhkhl6,
       if(m.sjjkhl7 = '', if(m.whkhl = '', ifnull(w.xs, ifnull(l.xs, 1)), m.whkhl), m.sjjkhl7)   sjhkhl7,
       if(m.sjjkhl8 = '', if(m.whkhl = '', ifnull(w.xs, ifnull(l.xs, 1)), m.whkhl), m.sjjkhl8)   sjhkhl8,
       if(m.sjjkhl9 = '', if(m.whkhl = '', ifnull(w.xs, ifnull(l.xs, 1)), m.whkhl), m.sjjkhl9)   sjhkhl9,
       if(m.sjjkhl10 = '', if(m.whkhl = '', ifnull(w.xs, ifnull(l.xs, 1)), m.whkhl), m.sjjkhl10) sjhkhl10,
       if(m.sjjkhl11 = '', if(m.whkhl = '', ifnull(w.xs, ifnull(l.xs, 1)), m.whkhl), m.sjjkhl11) sjhkhl11,
       if(m.sjjkhl12 = '', if(m.whkhl = '', ifnull(w.xs, ifnull(l.xs, 1)), m.whkhl), m.sjjkhl12) sjhkhl12
from dd_kz m
         left join whkhl w on m.zjedw = w.hb and w.kssj <= m.cq and w.jssj >= m.cq
         left join lrhl l on m.zjedw = l.hb and l.kssj <= m.cq and l.jssj >= m.cq where m.cq is not null and m.cq<>'';
