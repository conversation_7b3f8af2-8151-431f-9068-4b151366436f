drop table if exists dd_kz;
CREATE TABLE `dd_kz`
(
    `ddbh`     varchar(64) NOT NULL,
    `jkid`     varchar(64) NOT NULL,
    `khid`     int(11)     DEFAULT NULL COMMENT '客户',
    `cdrq`     varchar(32),
    `zgfs`     varchar(32),
    `fkfs`     varchar(32),
    `tzqr`     varchar(32),
    `jdtx`     varchar(32),
    `xsfp`     varchar(32),
    `xdgc`     varchar(32),
    `xdrq`     varchar(32),
    `clqr`     varchar(32),
    `ygchrq`   varchar(32),
    `nsap`     varchar(32),
    `kzpb`     varchar(32),
    `dqrq`     varchar(32),
    `dkxz`     varchar(32),
    `dmrq`     varchar(32),
    `zxrq`     varchar(32),
    `dctx`     varchar(32),
    `xztx`     varchar(32),
    `rmrq`     varchar(32),
    `pskz`     varchar(32),
    `yhaprq`   varchar(32),
    `cptqr`    varchar(32),
    `fhrq`     varchar(32),
    `cq`       varchar(32),
    `xzjs`     varchar(32),
    `xzbz`     varchar(64),
    `hgh`      varchar(32),
    `hch`      varchar(64),
    `mdg`      varchar(32),
    `yjdgsj`   varchar(32),
    `zdzz`     varchar(32),
    `yfzd`     varchar(32),
    `sftd`     varchar(32),
    `dgqyztx`  varchar(32),
    `mdgqg`    varchar(32),
    `kcsh`     varchar(32),
    `khfk`     varchar(32),
    `hkqk`     varchar(32),
    `hkje`     varchar(32),
    `hksj`     varchar(32),
    `hkjedw`   varchar(32),
    `hkje1`    varchar(32),
    `hksj1`    varchar(32),
    `hkjedw1`  varchar(32),
    `hkje2`    varchar(32),
    `hksj2`    varchar(32),
    `hkjedw2`  varchar(32),
    `hkje3`    varchar(32),
    `hksj3`    varchar(32),
    `hkjedw3`  varchar(32),
    `hkje4`    varchar(32),
    `hksj4`    varchar(32),
    `hkjedw4`  varchar(32),
    `hkje5`    varchar(32),
    `hkje6`    varchar(32),
    `hkje7`    varchar(32),
    `hkje8`    varchar(32),
    `hkje9`    varchar(32),
    `hkje10`   varchar(32),
    `hkje11`   varchar(32),
    `hkje12`   varchar(32),
    `hksj5`    varchar(32),
    `hksj6`    varchar(32),
    `hksj7`    varchar(32),
    `hksj8`    varchar(32),
    `hksj9`    varchar(32),
    `hksj10`   varchar(32),
    `hksj11`   varchar(32),
    `hksj12`   varchar(32),
    `hkjedw5`  varchar(32),
    `hkjedw6`  varchar(32),
    `hkjedw7`  varchar(32),
    `hkjedw8`  varchar(32),
    `hkjedw9`  varchar(32),
    `hkjedw10` varchar(32),
    `hkjedw11` varchar(32),
    `hkjedw12` varchar(32),
    `hkfs`     varchar(32),
    `hkfs1`    varchar(32),
    `hkfs2`    varchar(32),
    `hkfs3`    varchar(32),
    `hkfs4`    varchar(32),
    `hkfs5`    varchar(32),
    `hkfs6`    varchar(32),
    `hkfs7`    varchar(32),
    `hkfs8`    varchar(32),
    `hkfs9`    varchar(32),
    `hkfs10`   varchar(32),
    `hkfs11`   varchar(32),
    `hkfs12`   varchar(32),
    `sjjkhl`   varchar(32),
    `sjjkhl1`  varchar(32),
    `sjjkhl2`  varchar(32),
    `sjjkhl3`  varchar(32),
    `sjjkhl4`  varchar(32),
    `sjjkhl5`  varchar(32),
    `sjjkhl6`  varchar(32),
    `sjjkhl7`  varchar(32),
    `sjjkhl8`  varchar(32),
    `sjjkhl9`  varchar(32),
    `sjjkhl10` varchar(32),
    `sjjkhl11` varchar(32),
    `sjjkhl12` varchar(32),
    `whkhl`    varchar(32),
    `dduyf`    varchar(32),
    `zje`      varchar(32),
    `zjedw`    varchar(32),
    `spje`     varchar(32),
    `spjedw`   varchar(32),
    `qtce`     varchar(32),
    `cs`       varchar(128),
    `ddzt`     varchar(32),
    `sjdj`     varchar(32),
    `yf`       varchar(32),
    `gz`       varchar(32),
    `qg`       varchar(32),
    `sh`       varchar(32),
    `sj`       varchar(32),
    `yfbz`     varchar(32),
    `gzbz`     varchar(32),
    `qgbz`     varchar(32),
    `shbz`     varchar(32),
    `sjbz`     varchar(32),
    `yfhl`     varchar(32),
    `gzhl`     varchar(32),
    `qghl`     varchar(32),
    `shhl`     varchar(32),
    `sjhl`     varchar(32),
    `hcb1`     varchar(32),
    `hcb2`     varchar(32),
    `hcb3`     varchar(32),
    `hcb4`     varchar(32),
    `tc1`      varchar(32),
    `tc2`      varchar(32),
    `spgccdk`  varchar(32),
    `spywcdk`  varchar(32),
    `sphdcdk`  varchar(32),
    `spqtcdk`  varchar(32),
    `hzyf`     varchar(32),
    `tchyf`    varchar(32),
    `tcsp`     varchar(32),
    `hz`       varchar(32),
    `lrsj`     varchar(32) DEFAULT NULL COMMENT '录入时间',
    `hl`       varchar(32) DEFAULT '整柜' COMMENT '货量',
    `gxsl`     varchar(32) DEFAULT '1*20gp' COMMENT '柜型数量',
    `gclx`     varchar(64) DEFAULT NULL COMMENT '工厂类型',
    `lx`       varchar(32) DEFAULT '正常订单' COMMENT '订单类型',
    PRIMARY KEY (`ddbh`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

insert into dd_kz (ddbh,jkid, cdrq, zgfs, fkfs, tzqr, jdtx, xsfp, xdgc, xdrq, clqr, ygchrq, nsap, kzpb, dqrq, dkxz, dmrq,
                   zxrq, dctx, xztx, rmrq, pskz, yhaprq, cptqr, fhrq, cq, xzjs, xzbz, hgh, hch, mdg, yjdgsj, zdzz, yfzd,
                   sftd, dgqyztx, mdgqg, kcsh, khfk, hkqk, hkje, hksj, hkjedw, hkje1, hksj1, hkjedw1, hkje2, hksj2,
                   hkjedw2, hkje3, hksj3, hkjedw3, hkje4, hksj4, hkjedw4, hkje5, hkje6, hkje7, hkje8, hkje9, hkje10,
                   hkje11, hkje12, hksj5, hksj6, hksj7, hksj8, hksj9, hksj10, hksj11, hksj12, hkjedw5, hkjedw6, hkjedw7,
                   hkjedw8, hkjedw9, hkjedw10, hkjedw11, hkjedw12, hkfs, hkfs1, hkfs2, hkfs3, hkfs4, hkfs5, hkfs6,
                   hkfs7, hkfs8, hkfs9, hkfs10, hkfs11, hkfs12, sjjkhl, sjjkhl1, sjjkhl2, sjjkhl3, sjjkhl4, sjjkhl5,
                   sjjkhl6, sjjkhl7, sjjkhl8, sjjkhl9, sjjkhl10, sjjkhl11, sjjkhl12, whkhl, dduyf, zje, zjedw, spje,
                   spjedw, qtce, cs, ddzt, sjdj, yf, gz, qg, sh, sj, yfbz, gzbz, qgbz, shbz, sjbz, yfhl, gzhl, qghl,
                   shhl, sjhl, hcb1, hcb2, hcb3, hcb4, tc1, tc2, spgccdk, spywcdk, sphdcdk, spqtcdk, hzyf, tchyf, tcsp,
                   hz)
select jkid,
       jkid,
       cdrq,
       zgfs,
       fkfs,
       tzqr,
       jdtx,
       xsfp,
       xdgc,
       xdrq,
       clqr,
       ygchrq,
       nsap,
       kzpb,
       dqrq,
       dkxz,
       dmrq,
       zxrq,
       dctx,
       xztx,
       rmrq,
       pskz,
       yhaprq,
       cptqr,
       fhrq,
       cq,
       xzjs,
       xzbz,
       hgh,
       hch,
       mdg,
       yjdgsj,
       zdzz,
       yfzd,
       sftd,
       dgqyztx,
       mdgqg,
       kcsh,
       khfk,
       hkqk,
       hkje,
       hksj,
       hkjedw,
       hkje1,
       hksj1,
       hkjedw1,
       hkje2,
       hksj2,
       hkjedw2,
       hkje3,
       hksj3,
       hkjedw3,
       hkje4,
       hksj4,
       hkjedw4,
       hkje5,
       hkje6,
       hkje7,
       hkje8,
       hkje9,
       hkje10,
       hkje11,
       hkje12,
       hksj5,
       hksj6,
       hksj7,
       hksj8,
       hksj9,
       hksj10,
       hksj11,
       hksj12,
       hkjedw5,
       hkjedw6,
       hkjedw7,
       hkjedw8,
       hkjedw9,
       hkjedw10,
       hkjedw11,
       hkjedw12,
       hkfs,
       hkfs1,
       hkfs2,
       hkfs3,
       hkfs4,
       hkfs5,
       hkfs6,
       hkfs7,
       hkfs8,
       hkfs9,
       hkfs10,
       hkfs11,
       hkfs12,
       sjjkhl,
       sjjkhl1,
       sjjkhl2,
       sjjkhl3,
       sjjkhl4,
       sjjkhl5,
       sjjkhl6,
       sjjkhl7,
       sjjkhl8,
       sjjkhl9,
       sjjkhl10,
       sjjkhl11,
       sjjkhl12,
       whkhl,
       dduyf,
       zje,
       zjedw,
       spje,
       spjedw,
       qtce,
       cs,
       ddzt,
       sjdj,
       yf,
       gz,
       qg,
       sh,
       sj,
       yfbz,
       gzbz,
       qgbz,
       shbz,
       sjbz,
       yfhl,
       gzhl,
       qghl,
       shhl,
       sjhl,
       hcb1,
       hcb2,
       hcb3,
       hcb4,
       tc1,
       tc2,
       spgccdk,
       spywcdk,
       sphdcdk,
       spqtcdk,
       hzyf,
       tchyf,
       tcsp,
       hz
from vw_jkmx;

drop view vw_jkmx;
drop view vw_jkmx_n;
drop view vw_jkxm;

create or replace view vw_tc as
select `x`.`xm`    AS `xm`,
       `x`.`cq`    AS `cq`,
       `x`.`kh`    AS `kh`,
       `x`.`ddbh`  AS `ddbh`,
       `x`.`tchyf` AS `tchyf`,
       `x`.`tcsp`  AS `tcsp`,
       `x`.`zje`   AS `zje`,
       `x`.`hz`    AS `hz`,
       `x`.`hb`    AS `hb`,
       `x`.`rmb`   AS `rmb`,
       `x`.`xs`    AS `xs`,
       `x`.`tc`    AS `tc`
from (select `a`.`xm`                                                                                                                           AS `xm`,
             `v`.`cq`                                                                                                                           AS `cq`,
             `d`.`kh`                                                                                                                           AS `kh`,
             `v`.`jkid`                                                                                                                         AS `ddbh`,
             `v`.`tchyf`                                                                                                                        AS `tchyf`,
             `v`.`tcsp`                                                                                                                         AS `tcsp`,
             `v`.`zje`                                                                                                                          AS `zje`,
             `v`.`hz`                                                                                                                           AS `hz`,
             `k`.`hb`                                                                                                                           AS `hb`,
             round(((((`v`.`zje` - `v`.`tchyf`) - `v`.`tcsp`) * `l`.`xs`) - ifnull(`v`.`hz`, 0)), 3)                                            AS `rmb`,
             `t`.`xs`                                                                                                                           AS `xs`,
             if(ifnull(d.ddje, 0) = 0, 0, round((((((`v`.`zje` - `v`.`tchyf`) - `v`.`tcsp`) * `l`.`xs`) - ifnull(`v`.`hz`, 0)) * `t`.`xs`), 2)) AS `tc`
      from (((((`dd` `d` join `kh` `k`) join `tc` `t`) join `dd_kz` `v`) join `account` `a`) join `lrhl` `l`)
      where ((`d`.`lx` = '正常订单') and (`d`.`kh` = `k`.`id`) and (`v`.`jkid` = `d`.`ddbh`) and (`k`.`id` = `t`.`khid`) and (`t`.`kssj` <= `v`.`cq`) and (`t`.`jssj` >= `v`.`cq`) and (`a`.`id` = `t`.`ywyid`) and (`v`.`hkqk` = '已回全款') and (`v`.`zjedw` = `l`.`hb`) and (`v`.`cq` >= `l`.`kssj`) and (`v`.`cq` <= `l`.`jssj`) and (`v`.`jkid` <> '刘总的雕刻')
          and ((`k`.`hzsj` < '2021-01-01') or ((`k`.`hzsj` >= '2021-01-01') and (`d`.`mll` >= 0.05))))
        and d.ddbh not in (select distinct ddbh from ddtcxs)
      union all
      select `a`.`xm`                                                                                AS `xm`,
             `v`.`cq`                                                                                AS `cq`,
             `d`.`kh`                                                                                AS `kh`,
             `v`.`jkid`                                                                              AS `ddbh`,
             `v`.`tchyf`                                                                             AS `tchyf`,
             `v`.`tcsp`                                                                              AS `tcsp`,
             `v`.`zje`                                                                               AS `zje`,
             `v`.`hz`                                                                                AS `hz`,
             `k`.`hb`                                                                                AS `hb`,
             round(((((`v`.`zje` - `v`.`tchyf`) - `v`.`tcsp`) * `l`.`xs`) - ifnull(`v`.`hz`, 0)), 3) AS `rmb`,
             `t`.`xs`                                                                                AS `xs`,
             '0'                                                                                     AS `tc`
      from (((((`dd` `d` join `kh` `k`) join `tc` `t`) join `dd_kz` `v`) join `account` `a`) join `lrhl` `l`)
      where ((`d`.`lx` = '正常订单') and (`d`.`kh` = `k`.`id`) and (`v`.`jkid` = `d`.`ddbh`) and (`k`.`id` = `t`.`khid`) and (`t`.`kssj` <= `v`.`cq`) and (`t`.`jssj` >= `v`.`cq`) and (`a`.`id` = `t`.`ywyid`) and (`v`.`hkqk` = '已回全款') and (`v`.`zjedw` = `l`.`hb`) and (`v`.`cq` >= `l`.`kssj`) and (`v`.`cq` <= `l`.`jssj`) and (`v`.`jkid` <> '刘总的雕刻') and (`k`.`hzsj` >= '2021-01-01') and
             (`d`.`mll` < 0.05))
        and d.ddbh not in (select distinct ddbh from ddtcxs)
      union all
      select `a`.`xm`                                                                                                                           AS `xm`,
             `v`.`cq`                                                                                                                           AS `cq`,
             `d`.`kh`                                                                                                                           AS `kh`,
             `v`.`jkid`                                                                                                                         AS `ddbh`,
             `v`.`tchyf`                                                                                                                        AS `tchyf`,
             `v`.`tcsp`                                                                                                                         AS `tcsp`,
             `v`.`zje`                                                                                                                          AS `zje`,
             `v`.`hz`                                                                                                                           AS `hz`,
             `d`.`hb`                                                                                                                           AS `hb`,
             round(((((`v`.`zje` - `v`.`tchyf`) - `v`.`tcsp`) * `l`.`xs`) - ifnull(`v`.`hz`, 0)), 3)                                            AS `rmb`,
             `t`.`xs`                                                                                                                           AS `xs`,
             if(ifnull(d.ddje, 0) = 0, 0, round((((((`v`.`zje` - `v`.`tchyf`) - `v`.`tcsp`) * `l`.`xs`) - ifnull(`v`.`hz`, 0)) * `t`.`xs`), 2)) AS `tc`
      from ((((`dd` `d` join `ddtcxs` `t`) join `dd_kz` `v`) join `account` `a`) join `lrhl` `l`)
      where ((`d`.`lx` = '正常订单') and (`v`.`jkid` = `d`.`ddbh`) and (`a`.`id` = `t`.`ywyid`) and (`v`.`hkqk` = '已回全款') and (`v`.`zjedw` = `l`.`hb`) and (`v`.`cq` >= `l`.`kssj`) and (`v`.`cq` <= `l`.`jssj`) and (`v`.`jkid` <> '刘总的雕刻')
          and (`d`.`mll` >= 0.05) and d.ddbh in (select distinct ddbh from ddtcxs))) `x`
order by `x`.`xm`, `x`.`cq`;

create or replace view vw_yttc as
select *
from (select d.ddbh, a.xm, v.cq, if(ifnull(d.ddje, 0) = 0, 0, round(((v.zje - v.tchyf - v.tcsp) * l.xs - ifnull(d.hz, 0)) * t.xs, 2)) tc
      from dd d,
           kh k,
           tc t,
           dd_kz v,
           account a,
           lrhl l
      where d.lx = '正常订单'
        and d.ddbh <> '刘总的雕刻'
        and d.kh = k.id
        and v.jkid = d.ddbh
        and k.id = t.khid
        and a.id = t.ywyid
        and t.kssj <= v.cq
        and t.jssj >= v.cq
        and v.zjedw = l.hb
        and v.cq >= l.kssj
        and v.cq <= l.jssj
        and d.ddbh not in (select distinct ddbh from ddtcxs)
        and (((`k`.`hzsj` >= '2021-01-01') and (`d`.`mll` >= 0.05)) or (k.hzsj <= '2021-01-01'))
      union all
      select d.ddbh, a.xm, v.cq, 0 tc
      from dd d,
           kh k,
           tc t,
           dd_kz v,
           account a,
           lrhl l
      where d.lx = '正常订单'
        and d.ddbh <> '刘总的雕刻'
        and d.kh = k.id
        and v.jkid = d.ddbh
        and k.id = t.khid
        and a.id = t.ywyid
        and t.kssj <= v.cq
        and t.jssj >= v.cq
        and v.zjedw = l.hb
        and v.cq >= l.kssj
        and v.cq <= l.jssj
        and `k`.`hzsj` >= '2021-01-01'
        and d.ddbh not in (select distinct ddbh from ddtcxs)
        and `d`.`mll` < 0.05
      union all
      select d.ddbh, a.xm, v.cq, if(ifnull(d.ddje, 0) = 0, 0, round(((v.zje - v.tchyf - v.tcsp) * l.xs - ifnull(d.hz, 0)) * t.xs, 2)) tc
      from dd d,
           ddtcxs t,
           dd_kz v,
           account a,
           lrhl l
      where d.lx = '正常订单'
        and d.ddbh <> '刘总的雕刻'
        and v.jkid = d.ddbh
        and d.ddbh = t.ddbh
        and a.id = t.ywyid
        and v.zjedw = l.hb
        and v.cq >= l.kssj
        and v.cq <= l.jssj
        and `d`.`mll` >= 0.05) z
order by xm, cq;

create or replace view vw_dd_hkhl as
select m.jkid                                                                      ddbh,
       if(m.whkhl = '', ifnull(w.xs, ifnull(l.xs, '')), m.whkhl)                                  whkhl,
       if(m.sjjkhl = '', if(m.whkhl = '', ifnull(w.xs, ifnull(l.xs, 1)), m.whkhl), m.sjjkhl)     sjhkhl,
       if(m.sjjkhl1 = '', if(m.whkhl = '', ifnull(w.xs, ifnull(l.xs, 1)), m.whkhl), m.sjjkhl1)   sjhkhl1,
       if(m.sjjkhl2 = '', if(m.whkhl = '', ifnull(w.xs, ifnull(l.xs, 1)), m.whkhl), m.sjjkhl2)   sjhkhl2,
       if(m.sjjkhl3 = '', if(m.whkhl = '', ifnull(w.xs, ifnull(l.xs, 1)), m.whkhl), m.sjjkhl3)   sjhkhl3,
       if(m.sjjkhl4 = '', if(m.whkhl = '', ifnull(w.xs, ifnull(l.xs, 1)), m.whkhl), m.sjjkhl4)   sjhkhl4,
       if(m.sjjkhl5 = '', if(m.whkhl = '', ifnull(w.xs, ifnull(l.xs, 1)), m.whkhl), m.sjjkhl5)   sjhkhl5,
       if(m.sjjkhl6 = '', if(m.whkhl = '', ifnull(w.xs, ifnull(l.xs, 1)), m.whkhl), m.sjjkhl6)   sjhkhl6,
       if(m.sjjkhl7 = '', if(m.whkhl = '', ifnull(w.xs, ifnull(l.xs, 1)), m.whkhl), m.sjjkhl7)   sjhkhl7,
       if(m.sjjkhl8 = '', if(m.whkhl = '', ifnull(w.xs, ifnull(l.xs, 1)), m.whkhl), m.sjjkhl8)   sjhkhl8,
       if(m.sjjkhl9 = '', if(m.whkhl = '', ifnull(w.xs, ifnull(l.xs, 1)), m.whkhl), m.sjjkhl9)   sjhkhl9,
       if(m.sjjkhl10 = '', if(m.whkhl = '', ifnull(w.xs, ifnull(l.xs, 1)), m.whkhl), m.sjjkhl10) sjhkhl10,
       if(m.sjjkhl11 = '', if(m.whkhl = '', ifnull(w.xs, ifnull(l.xs, 1)), m.whkhl), m.sjjkhl11) sjhkhl11,
       if(m.sjjkhl12 = '', if(m.whkhl = '', ifnull(w.xs, ifnull(l.xs, 1)), m.whkhl), m.sjjkhl12) sjhkhl12
from dd_kz m
         left join whkhl w on m.zjedw = w.hb and w.kssj <= m.cq and w.jssj >= m.cq
         left join lrhl l on m.zjedw = l.hb and l.kssj <= m.cq and l.jssj >= m.cq where m.cq is not null and m.cq<>'';

create or replace view vw_dd_hkrmb as
select m.jkid                                      ddbh,
       l.whkhl                                     whkhl,
       ifnull(m.hkje, 0) +
       ifnull(m.hkje1, 0) +
       ifnull(m.hkje2, 0) +
       ifnull(m.hkje3, 0) +
       ifnull(m.hkje4, 0) +
       ifnull(m.hkje5, 0) +
       ifnull(m.hkje6, 0) +
       ifnull(m.hkje7, 0) +
       ifnull(m.hkje8, 0) +
       ifnull(m.hkje9, 0) +
       ifnull(m.hkje10, 0) +
       ifnull(m.hkje11, 0) +
       ifnull(m.hkje12, 0)                         sjhk,
       ifnull(m.hkje, 0) * ifnull(l.sjhkhl, 0) +
       ifnull(m.hkje1, 0) * ifnull(l.sjhkhl1, 0) +
       ifnull(m.hkje2, 0) * ifnull(l.sjhkhl2, 0) +
       ifnull(m.hkje3, 0) * ifnull(l.sjhkhl3, 0) +
       ifnull(m.hkje4, 0) * ifnull(l.sjhkhl4, 0) +
       ifnull(m.hkje5, 0) * ifnull(l.sjhkhl5, 0) +
       ifnull(m.hkje6, 0) * ifnull(l.sjhkhl6, 0) +
       ifnull(m.hkje7, 0) * ifnull(l.sjhkhl7, 0) +
       ifnull(m.hkje8, 0) * ifnull(l.sjhkhl8, 0) +
       ifnull(m.hkje9, 0) * ifnull(l.sjhkhl9, 0) +
       ifnull(m.hkje10, 0) * ifnull(l.sjhkhl10, 0) +
       ifnull(m.hkje11, 0) * ifnull(l.sjhkhl11, 0) +
       ifnull(m.hkje12, 0) * ifnull(l.sjhkhl12, 0) sjhkrmb
from dd_kz m
         left join vw_dd_hkhl l on m.jkid = l.ddbh;

create or replace view vw_ddtj as
select v.cq                                                                                    cq,
       v.khid                                                                                  khid,
       v.jkid                                                                                  ddbh,
       v.cs                                                                                    cs,
       v.zje                                                                                   zje,
       v.dduyf                                                                                 dduyf,
       v.sjdj                                                                                  sjdj,
       v.zjedw                                                                                 zjedw,
       v.hkqk                                                                                  hkqk,
       v.hz                                                                                    hz,
       l.whkhl                                                                                 whkhl,
       round(l.sjhkrmb + (v.zje + v.dduyf + v.sjdj - l.sjhk) * l.whkhl, 3)                     dkrmb,
       round(l.sjhk, 3)                                                                        hkje,
       round(l.sjhkrmb, 3)                                                                     hkrmb,
       v.hkjedw                                                                                hkjedw,
       round(v.spje, 3)                                                                        spje,
       round(v.zje + v.dduyf + v.sjdj - l.sjhk, 3)                                             cha,
       round(v.qtce, 3)                                                                        qtce,
       round(v.qtce * l.whkhl, 3)                                                              qtcermb,
       round(v.hzyf, 3)                                                                        hzyf,
       ifnull(b.cb, 0)                                                                         clcb,
       round(ifnull(w.alje, 0), 3)                                                             alje,
       round(ifnull(c.kdf, 0), 0)                                                              kdf,
       round(ifnull(d.pj, 0), 0)                                                               pj,
       round((case v.hkqk
                  when '已回全款' then l.sjhkrmb - v.hzyf - ifnull(w.alje, 0) - ifnull(c.kdf, 0) - ifnull(b.cb, 0) - ifnull(d.pj, 0)
                  else l.sjhkrmb + (v.zje + v.dduyf + v.sjdj - l.sjhk) * l.whkhl - v.hzyf - ifnull(w.alje, 0) - ifnull(c.kdf, 0) - ifnull(b.cb, 0) - ifnull(d.pj, 0) end), 3) ml
from dd_kz v
         left join vw_dd_hkrmb l on v.jkid = l.ddbh
         left join vw_wjg w on v.jkid = w.ddbh
         left join vw_clcb b on v.jkid = b.ddbh
         left join vw_kdf c on v.jkid = c.ddbh
         left join vw_bx d on v.jkid = d.ddbh
where v.cq >= '2021-01-01'
union all
select v.cq                                                                                    cq,
       v.khid                                                                                  khid,
       v.jkid                                                                                  ddbh,
       v.cs                                                                                    cs,
       v.zje                                                                                   zje,
       v.dduyf                                                                                 dduyf,
       v.sjdj                                                                                  sjdj,
       v.zjedw                                                                                 zjedw,
       v.hkqk                                                                                  hkqk,
       v.hz                                                                                    hz,
       l.whkhl                                                                                 whkhl,
       round(l.sjhkrmb + (v.zje + v.dduyf - l.sjhk) * l.whkhl, 3)                              dkrmb,
       round(l.sjhk, 3)                                                                        hkje,
       round(l.sjhkrmb, 3)                                                                     hkrmb,
       v.hkjedw                                                                                hkjedw,
       round(v.spje, 3)                                                                        spje,
       round(v.zje + v.dduyf - l.sjhk, 3)                                                      cha,
       round(v.qtce, 3)                                                                        qtce,
       round(v.qtce * l.whkhl, 3)                                                              qtcermb,
       round(v.hzyf, 3)                                                                        hzyf,
       ifnull(b.cb, 0)                                                                         clcb,
       round(ifnull(w.alje, 0), 3)                                                             alje,
       round(ifnull(c.kdf, 0), 0)                                                              kdf,
       round(ifnull(d.pj, 0), 0)                                                               pj,
       round((case v.hkqk
                  when '已回全款' then l.sjhkrmb - v.hzyf - ifnull(w.alje, 0) - ifnull(c.kdf, 0) - ifnull(b.cb, 0) - ifnull(d.pj, 0)
                  else l.sjhkrmb + (v.zje + v.dduyf - l.sjhk) * l.whkhl - v.hzyf - ifnull(w.alje, 0) - ifnull(c.kdf, 0) - ifnull(b.cb, 0) - ifnull(d.pj, 0) end), 3) ml
from dd_kz v
         left join vw_dd_hkrmb l on v.jkid = l.ddbh
         left join vw_wjg w on v.jkid = w.ddbh
         left join vw_clcb b on v.jkid = b.ddbh
         left join vw_kdf c on v.jkid = c.ddbh
         left join vw_bx d on v.jkid = d.ddbh
where v.cq < '2021-01-01';