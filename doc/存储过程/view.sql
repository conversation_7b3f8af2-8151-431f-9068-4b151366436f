create or replace view vw_cpmx as
select *,
       case cddw
           when 'INCH' then sl * a * b * c * 2.54 * 2.54 * 2.54 * 36 / 1000000
           when 'CM' then sl * a * b * c * 36 / 1000000
           when 'MM' then sl * a * b * c * 36 / 1000000000
           else sl * a * b * c * 36 / 1000000 end cai
from cpmx;

create or replace view vw_dd_hd as
select `x`.`ddbh` AS `ddbh`, ifnull(`z`.`hdid`, 1) AS `hdid`
from (`dd` `x` left join `dzgz` `z`
      on (((`x`.`kh` = `z`.`khid`) and (`x`.`lrsj` >= `z`.`ksrq`) and (`x`.`lrsj` <= `z`.`jsrq`))))
where ((`x`.`lx` = '正常订单') and
       (not (exists(select `dx1`.`ddbh` from `dzgzdd` `dx1` where (`x`.`ddbh` = `dx1`.`ddbh`)))))
union
select `dx`.`ddbh` AS `ddbh`, `dx`.`hdid` AS `hdid`
from `dzgzdd` `dx`
where (`dx`.`hdid` <> 26)
union
select `dx`.`ddbh` AS `ddbh`, 1 AS `hdid`
from `dzgzdd` `dx`
where (`dx`.`hdid` <> 26)
union
select `dx`.`ddbh` AS `ddbh`, `dx`.`hdid` AS `hdid`
from `dzgzdd` `dx`
where (`dx`.`hdid` = 26);

create or replace view vw_ddmx_hj as
select `ddbh`                 AS `ddbh`,
       `gcdd`                 AS `gcdd`,
       max(ifnull(hb, ''))    AS 'zjedw',
       sum(`sl`)              AS `zsl`,
       round(sum(`mhzj`), 2)  AS `zje`,
       round(sum(`cs`), 3)    AS `zcs`,
       round(sum(`zl`), 2)    AS `zzl`,
       round(sum(`zllbs`), 3) AS `zzllbs`,
       round(sum(`lfs`), 4)   AS `zlfs`
from ddmx
group by `ddbh`, `gcdd`;

create or replace view vw_dd_whkhl as
select ddbh, d.hb, d.cq, if(d.whkhl = '0' or d.whkhl is null, ifnull(w.xs, ifnull(l.xs, 1)), d.whkhl) whkhl
from dd d
         left join whkhl w on d.cq >= w.kssj and d.cq <= w.jssj and d.hb = w.hb
         left join lrhl l on d.cq >= l.kssj and d.cq <= l.jssj and d.hb = l.hb
where ((d.cq is not null and d.cq <> '' and (d.whkhl is null or d.whkhl = '0'))
    or (d.whkhl is not null and d.whkhl <> '0'))
  and d.hb is not null
  and d.hb <> ''
group by d.ddbh, d.hb, d.cq, d.whkhl;

create or replace view vw_dd_hkhl as
select m.jkid                                                                                                 ddbh,
       if(m.whkhl is null or m.whkhl = '', w.whkhl, m.whkhl)                                                  whkhl,
       if(m.sjjkhl is null or m.sjjkhl = '', if(m.whkhl is null or m.whkhl = '', w.whkhl, m.whkhl), m.sjjkhl) sjjkhl,
       if(m.sjjkhl1 is null or m.sjjkhl1 = '', if(m.whkhl is null or m.whkhl = '', w.whkhl, m.whkhl),
          m.sjjkhl1)                                                                                          sjjkhl1,
       if(m.sjjkhl2 is null or m.sjjkhl2 = '', if(m.whkhl is null or m.whkhl = '', w.whkhl, m.whkhl),
          m.sjjkhl2)                                                                                          sjjkhl2,
       if(m.sjjkhl3 is null or m.sjjkhl3 = '', if(m.whkhl is null or m.whkhl = '', w.whkhl, m.whkhl),
          m.sjjkhl3)                                                                                          sjjkhl3,
       if(m.sjjkhl4 is null or m.sjjkhl4 = '', if(m.whkhl is null or m.whkhl = '', w.whkhl, m.whkhl),
          m.sjjkhl4)                                                                                          sjjkhl4,
       if(m.sjjkhl5 is null or m.sjjkhl5 = '', if(m.whkhl is null or m.whkhl = '', w.whkhl, m.whkhl),
          m.sjjkhl5)                                                                                          sjjkhl5,
       if(m.sjjkhl6 is null or m.sjjkhl6 = '', if(m.whkhl is null or m.whkhl = '', w.whkhl, m.whkhl),
          m.sjjkhl6)                                                                                          sjjkhl6,
       if(m.sjjkhl7 is null or m.sjjkhl7 = '', if(m.whkhl is null or m.whkhl = '', w.whkhl, m.whkhl),
          m.sjjkhl7)                                                                                          sjjkhl7,
       if(m.sjjkhl8 is null or m.sjjkhl8 = '', if(m.whkhl is null or m.whkhl = '', w.whkhl, m.whkhl),
          m.sjjkhl8)                                                                                          sjjkhl8,
       if(m.sjjkhl9 is null or m.sjjkhl9 = '', if(m.whkhl is null or m.whkhl = '', w.whkhl, m.whkhl),
          m.sjjkhl9)                                                                                          sjjkhl9,
       if(m.sjjkhl10 is null or m.sjjkhl10 = '', if(m.whkhl is null or m.whkhl = '', w.whkhl, m.whkhl),
          m.sjjkhl10)                                                                                         sjjkhl10,
       if(m.sjjkhl11 is null or m.sjjkhl11 = '', if(m.whkhl is null or m.whkhl = '', w.whkhl, m.whkhl),
          m.sjjkhl11)                                                                                         sjjkhl11,
       if(m.sjjkhl12 is null or m.sjjkhl12 = '', if(m.whkhl is null or m.whkhl = '', w.whkhl, m.whkhl),
          m.sjjkhl12)                                                                                         sjjkhl12
from dd_kz m
         left join vw_dd_whkhl w on m.ddbh = w.ddbh;

create or replace view vw_dd_hkrmb as
select m.jkid                                                ddbh,
       l.whkhl                                               whkhl,
       ifnull(m.hkje, 0) +
       ifnull(m.hkje1, 0) +
       ifnull(m.hkje2, 0) +
       ifnull(m.hkje3, 0) +
       ifnull(m.hkje4, 0) +
       ifnull(m.hkje5, 0) +
       ifnull(m.hkje6, 0) +
       ifnull(m.hkje7, 0) +
       ifnull(m.hkje8, 0) +
       ifnull(m.hkje9, 0) +
       ifnull(m.hkje10, 0) +
       ifnull(m.hkje11, 0) +
       ifnull(m.hkje12, 0)                                   sjhk,
       round(ifnull(m.hkje, 0) * ifnull(l.sjjkhl, 0), 2) +
       round(ifnull(m.hkje1, 0) * ifnull(l.sjjkhl1, 0), 2) +
       round(ifnull(m.hkje2, 0) * ifnull(l.sjjkhl2, 0), 2) +
       round(ifnull(m.hkje3, 0) * ifnull(l.sjjkhl3, 0), 2) +
       round(ifnull(m.hkje4, 0) * ifnull(l.sjjkhl4, 0), 2) +
       round(ifnull(m.hkje5, 0) * ifnull(l.sjjkhl5, 0), 2) +
       round(ifnull(m.hkje6, 0) * ifnull(l.sjjkhl6, 0), 2) +
       round(ifnull(m.hkje7, 0) * ifnull(l.sjjkhl7, 0), 2) +
       round(ifnull(m.hkje8, 0) * ifnull(l.sjjkhl8, 0), 2) +
       round(ifnull(m.hkje9, 0) * ifnull(l.sjjkhl9, 0), 2) +
       round(ifnull(m.hkje10, 0) * ifnull(l.sjjkhl10, 0), 2) +
       round(ifnull(m.hkje11, 0) * ifnull(l.sjjkhl11, 0), 2) +
       round(ifnull(m.hkje12, 0) * ifnull(l.sjjkhl12, 0), 2) sjhkrmb
from dd_kz m
         left join vw_dd_hkhl l on m.jkid = l.ddbh;

-- 每个订单的几个毛利
create or replace view vw_ddml as
select v.ddbh                                                                                                                                                              ddbh,
       round(ifnull(d.ml, 0) + ifnull(d.yszkrmb, 0), 2)                                                                                                                    ml,
       round(ifnull(d.ml, 0) - ifnull(v.dm_fj, 0) - ifnull(v.rm_fj, 0) - ifnull(v.zx_fj, 0) - ifnull(v.cl_fj, 0), 2)                                                       xzml,
       round((ifnull(d.ml, 0) - ifnull(v.dm_fj, 0) - ifnull(v.rm_fj, 0) - ifnull(v.zx_fj, 0) - ifnull(v.cl_fj, 0)) * 100 / ifnull(d.ddjermb, 0), 2)                        xzmll,
       round(ifnull(d.ml, 0) + ifnull(d.yszkrmb, 0) - ifnull(v.dm_fj, 0) - ifnull(v.rm_fj, 0) - ifnull(v.zx_fj, 0) - ifnull(v.cl_fj, 0), 2)                                lrml,
       round((ifnull(d.ml, 0) + ifnull(d.yszkrmb, 0) - ifnull(v.dm_fj, 0) - ifnull(v.rm_fj, 0) - ifnull(v.zx_fj, 0) - ifnull(v.cl_fj, 0)) * 100 / ifnull(d.ddjermb, 0), 2) lrmll,
       round((ifnull(d.ml, 0) + ifnull(d.yszkrmb, 0)) * 100 / ifnull(d.ddjermb, 0), 2)                                                                                     mll
from dd_kz v
         left join dd d on v.ddbh = d.ddbh;

-- 每个订单的提成系数和汇率系数
create or replace view vw_ddtcxs as
select d.ddbh ddbh, d.khid khid, a.xm xm, t.kssj kssj, t.jssj jssj, t.xs xs, l.xs lxs
from dd_kz d,
     lrhl l,
     tc t
         left join account a on t.ywyid = a.id
where d.khid = t.khid
  and d.cq >= t.kssj
  and d.cq <= t.jssj
  and d.cq >= l.kssj
  and d.cq <= l.jssj
  and d.zjedw = l.hb
  and d.ddbh not in (select distinct ddbh from ddtcxs)
  and (d.hz is null or d.hz='' or abs(d.hz)<1)
  and d.lx='正常订单'
union all
select d.ddbh ddbh, d.khid khid, a.xm xm, '2020-01-01' kssj, '2099-12-31' jssj, t.xs xs, l.xs lxs
from dd_kz d,
     lrhl l,
     ddtcxs t
         left join account a on t.ywyid = a.id
where d.ddbh = t.ddbh
  and d.cq >= l.kssj
  and d.cq <= l.jssj
  and d.zjedw = l.hb
  and (d.hz is null or d.hz='' or abs(d.hz)<1)
  and d.lx='正常订单';

create or replace view vw_ddtj as
select v.cq                                                                                    cq,
       v.khid                                                                                  khid,
       v.jkid                                                                                  ddbh,
       ifnull(v.ewsr, 0)                                                                       ewsr,
       ifnull(v.ewfy, 0)                                                                       ewfy,
       ifnull(v.cs, 0)                                                                         cs,
       ifnull(v.zje, 0)                                                                        zje,
       ifnull(v.dduyf, 0)                                                                      dduyf,
       ifnull(v.sjdj, 0)                                                                       sjdj,
       ifnull(v.zje, 0) + ifnull(v.dduyf, 0) + ifnull(v.sjdj, 0)                               hjje,
       v.zjedw                                                                                 zjedw,
       v.hkqk                                                                                  hkqk,
       ifnull(v.hz, 0)                                                                         hz,
       ifnull(l.whkhl, 0)                                                                      whkhl,
       round(ifnull(l.sjhkrmb, 0) +
             (ifnull(v.zje, 0) + ifnull(v.dduyf, 0) + ifnull(v.sjdj, 0) - ifnull(l.sjhk, 0)) * ifnull(l.whkhl, 0) - ifnull(v.hz, 0),
             3)                                                                                dkrmb,
       round(ifnull(l.sjhk, 0), 3)                                                             hkje,
       round(ifnull(l.sjhkrmb, 0), 3)                                                          hkrmb,
       v.hkjedw                                                                                hkjedw,
       round(ifnull(v.spje, 0), 3)                                                             spje,
       round(ifnull(v.tcsp, 0), 3)                                                             tcsp,
       round(ifnull(v.zje, 0) + ifnull(v.dduyf, 0) + ifnull(v.sjdj, 0) - ifnull(l.sjhk, 0), 3) cha,
       round(ifnull(v.qtce, 0), 3)                                                             qtce,
       round(ifnull(v.qtce, 0) * ifnull(l.whkhl, 0), 3)                                        qtcermb,
       round(ifnull(v.hzyf, 0), 3)                                                             hzyf,
       ifnull(ifnull(v.clcb, 0), 0)                                                            clcb,
       round(ifnull(v.alje, 0), 3)                                                             alje,
       round((case v.hkqk
                  when '已回全款' then ifnull(l.sjhkrmb, 0) + ifnull(ewsr, 0) - ifnull(ewfy, 0) - ifnull(v.hzyf, 0) - ifnull(v.alje, 0) - ifnull(d.kdf, 0) -
                                       ifnull(v.clcb, 0) - ifnull(d.ywxgfy, 0) - ifnull(d.yszkrmb, 0) - ifnull(v.hz, 0)
                  else ifnull(l.sjhkrmb, 0) +
                       (ifnull(v.zje, 0) + ifnull(v.dduyf, 0) + ifnull(v.sjdj, 0) - ifnull(l.sjhk, 0)) *
                       ifnull(l.whkhl, 0) + ifnull(ewsr, 0) - ifnull(ewfy, 0) - ifnull(v.hzyf, 0) - ifnull(v.alje, 0) - ifnull(d.kdf, 0) - ifnull(v.clcb, 0) -
                       ifnull(d.ywxgfy, 0) - ifnull(d.yszkrmb, 0) - ifnull(v.hz, 0) end), 3)                         ml
from dd_kz v
         left join dd d on v.ddbh = d.ddbh
         left join vw_dd_hkrmb l on v.jkid = l.ddbh
where (v.cq >= '2021-01-01' or v.cq is null or v.cq = '')
union all
select v.cq                                                                cq,
       v.khid                                                              khid,
       v.jkid                                                              ddbh,
       ifnull(v.ewsr, 0)                                                   ewsr,
       ifnull(v.ewfy, 0)                                                   ewfy,
       ifnull(v.cs, 0)                                                     cs,
       ifnull(v.zje, 0)                                                    zje,
       ifnull(v.dduyf, 0)                                                  dduyf,
       ifnull(v.sjdj, 0)                                                   sjdj,
       ifnull(v.zje, 0) + ifnull(v.dduyf, 0) + ifnull(v.sjdj, 0)           hjje,
       v.zjedw                                                             zjedw,
       v.hkqk                                                              hkqk,
       ifnull(v.hz, 0)                                                     hz,
       ifnull(l.whkhl, 0)                                                  whkhl,
       round(ifnull(l.sjhkrmb, 0) + (ifnull(v.zje, 0) + ifnull(v.dduyf, 0) - ifnull(l.sjhk, 0)) * ifnull(l.whkhl, 0) - ifnull(v.hz, 0),
             3)                                                            dkrmb,
       round(ifnull(l.sjhk, 0), 3)                                         hkje,
       round(ifnull(l.sjhkrmb, 0), 3)                                      hkrmb,
       v.hkjedw                                                            hkjedw,
       round(ifnull(v.spje, 0), 3)                                         spje,
       round(ifnull(v.tcsp, 0), 3)                                         tcsp,
       round(ifnull(v.zje, 0) + ifnull(v.dduyf, 0) - ifnull(l.sjhk, 0), 3) cha,
       round(ifnull(v.qtce, 0), 3)                                         qtce,
       round(ifnull(v.qtce, 0) * ifnull(l.whkhl, 0), 3)                    qtcermb,
       round(ifnull(v.hzyf, 0), 3)                                         hzyf,
       ifnull(ifnull(v.clcb, 0), 0)                                        clcb,
       round(ifnull(v.alje, 0), 3)                                         alje,
       round((case v.hkqk
                  when '已回全款' then ifnull(l.sjhkrmb, 0) + ifnull(ewsr, 0) - ifnull(ewfy, 0) - ifnull(v.hzyf, 0) - ifnull(v.alje, 0) - ifnull(d.kdf, 0) -
                                       ifnull(v.clcb, 0) - ifnull(d.ywxgfy, 0) - ifnull(v.hz, 0)
                  else ifnull(l.sjhkrmb, 0) +
                       (ifnull(v.zje, 0) + ifnull(v.dduyf, 0) - ifnull(l.sjhk, 0)) * ifnull(l.whkhl, 0) + ifnull(ewsr, 0) - ifnull(ewfy, 0) -
                       ifnull(v.hzyf, 0) - ifnull(v.alje, 0) - ifnull(d.kdf, 0) - ifnull(v.clcb, 0) -
                       ifnull(d.ywxgfy, 0) - ifnull(v.hz, 0) end), 3)      ml
from dd_kz v
         left join dd d on v.ddbh = d.ddbh
         left join vw_dd_hkrmb l on v.jkid = l.ddbh
where v.cq < '2021-01-01'
  and v.cq is not null
  and v.cq <> '';

create or replace view vw_khlxr as
select `id` AS `id`, `jc` AS `jc`, `khdylxrfd` AS `fd`, `khdylxrm` AS `mc`, `khdylxr` AS `yx`, 1 AS `xh`
from `kh`
where ((`khdylxrfd` is not null) or (`khdylxrm` is not null) or (`khdylxr` is not null))
union
select `id` AS `id`, `jc` AS `jc`, `khdelxrfd` AS `fd`, `khdelxrm` AS `mc`, `khdelxr` AS `yx`, 2 AS `xh`
from `kh`
where ((`khdelxrfd` is not null) or (`khdelxrm` is not null) or (`khdelxr` is not null))
union
select `id` AS `id`, `jc` AS `jc`, `khdslxrfd` AS `fd`, `khdslxrm` AS `mc`, `khdslxr` AS `yx`, 3 AS `xh`
from `kh`
where ((`khdslxrfd` is not null) or (`khdslxrm` is not null) or (`khdslxr` is not null))
union
select `id` AS `id`, `jc` AS `jc`, `khd4lxrfd` AS `fd`, `khd4lxrm` AS `mc`, `khd4lxr` AS `yx`, 4 AS `xh`
from `kh`
where ((`khd4lxrfd` is not null) or (`khd4lxrm` is not null) or (`khd4lxr` is not null))
union
select `id` AS `id`, `jc` AS `jc`, `khd5lxrfd` AS `fd`, `khd5lxrm` AS `mc`, `khd5lxr` AS `yx`, 5 AS `xh`
from `kh`
where ((`khd5lxrfd` is not null) or (`khd5lxrm` is not null) or (`khd5lxr` is not null))
union
select `id` AS `id`, `jc` AS `jc`, `khd6lxrfd` AS `fd`, `khd6lxrm` AS `mc`, `khd6lxr` AS `yx`, 6 AS `xh`
from `kh`
where ((`khd6lxrfd` is not null) or (`khd6lxrm` is not null) or (`khd6lxr` is not null))
union
select `id` AS `id`, `jc` AS `jc`, `khd7lxrfd` AS `fd`, `khd7lxrm` AS `mc`, `khd7lxr` AS `yx`, 7 AS `xh`
from `kh`
where ((`khd7lxrfd` is not null) or (`khd7lxrm` is not null) or (`khd7lxr` is not null))
union
select `id` AS `id`, `jc` AS `jc`, `khd8lxrfd` AS `fd`, `khd8lxrm` AS `mc`, `khd8lxr` AS `yx`, 8 AS `xh`
from `kh`
where ((`khd8lxrfd` is not null) or (`khd8lxrm` is not null) or (`khd8lxr` is not null));

create or replace view vw_khywycb as
select `d`.`kh` AS `kh`, `d`.`yf` AS `yf`, sum(`b`.`jcb`) AS `jcb`
from ((((select `kh` AS `kh`, substr(`cq`, 1, 7) AS `yf`
         from `dd`
         where ((`lx` = '正常订单') and (`cq` is not null) and (`cq` <> ''))
         group by `kh`, substr(`cq`, 1, 7))) `d` join `tc` `t`) join (select `t`.`ywyid`            AS `ywyid`,
                                                                             `d`.`yf`               AS `yf`,
                                                                             count(0)               AS `jls`,
                                                                             round(
                                                                                     (max((((ifnull(`g`.`gz`, 0) + ifnull(`g`.`jj`, 0)) +
                                                                                            ifnull(`g`.`ysb`, 0)) +
                                                                                           ifnull(`g`.`gjj`, 0))) /
                                                                                      count(0)), 0) AS `jcb`
                                                                      from (((((select `kh` AS `kh`, substr(`cq`, 1, 7) AS `yf`
                                                                                from `dd`
                                                                                where ((`lx` = '正常订单') and (`cq` is not null) and (`cq` <> ''))
                                                                                group by `kh`, substr(`cq`, 1, 7))) `d` join `tc` `t`) join `gz` `g`) join `account` `a`)
                                                                      where ((`d`.`kh` = `t`.`khid`) and
                                                                             (`d`.`yf` >= substr(`t`.`kssj`, 1, 7)) and
                                                                             (`d`.`yf` <= substr(`t`.`jssj`, 1, 7)) and
                                                                             (`g`.`xm` = `a`.`xm`) and
                                                                             (`a`.`id` = `t`.`ywyid`) and
                                                                             (`d`.`yf` = `g`.`yf`))
                                                                      group by `t`.`ywyid`, `d`.`yf`) `b`)
where ((`d`.`kh` = `t`.`khid`) and (`d`.`yf` >= substr(`t`.`kssj`, 1, 7)) and (`d`.`yf` <= substr(`t`.`jssj`, 1, 7)) and
       (`t`.`ywyid` = `b`.`ywyid`) and (`d`.`yf` = `b`.`yf`))
group by `d`.`kh`, `d`.`yf`;

-- 1.客户合作时间在2021年1月1日之前的，都按正常提成系数获得提成
-- 2.客户合作时间在2021年1月1日之后的，2023年10月1日之前的，需要判断毛利率是否大于0.05，才给发放提成
-- 3.客户合作时间在2023年10月1日之后的，需要判断费后毛利率是否大于0.05，才给发放提成
-- 总的提成 已回全款
create or replace view vw_yttc as
select `x`.`xm`,
       `z`.`cq`,
       `z`.`khid`                                                                                                                                                kh,
       `z`.`ddbh`,
       `z`.`tchyf`,
       `z`.`tcsp`,
       `z`.`zje`,
       `z`.`hz`,
       x.lxs,
       `z`.`zjedw`                                                                                                                                               hb,
       round((ifnull(`z`.`zje`, 0) - ifnull(`z`.`tchyf`, 0) - ifnull(`z`.`tcsp`, 0)) * ifnull(`x`.`lxs`, 0) - ifnull(`z`.`hz`, 0), 2)                         AS `rmb`,
       `x`.`xs`                                                                                                                                               AS `xs`,
       round(((ifnull(`z`.`zje`, 0) - ifnull(`z`.`tchyf`, 0) - ifnull(`z`.`tcsp`, 0)) * ifnull(`x`.`lxs`, 0) - ifnull(`z`.`hz`, 0)) * ifnull(`x`.`xs`, 0), 2) AS `tc`
from dd_kz z
         left join kh k on z.khid = k.id,
     vw_ddtcxs x,
     vw_ddml m
where k.hzsj < '2021-01-01'
  and z.ddbh = m.ddbh
  and z.ddbh = x.ddbh
union all
select `x`.`xm`,
       `z`.`cq`,
       `z`.`khid`                                                                                                                                                kh,
       `z`.`ddbh`,
       `z`.`tchyf`,
       `z`.`tcsp`,
       `z`.`zje`,
       `z`.`hz`,
       x.lxs,
       `z`.`zjedw`                                                                                                                                               hb,
       round((ifnull(`z`.`zje`, 0) - ifnull(`z`.`tchyf`, 0) - ifnull(`z`.`tcsp`, 0)) * ifnull(`x`.`lxs`, 0) - ifnull(`z`.`hz`, 0), 2)                         AS `rmb`,
       `x`.`xs`                                                                                                                                               AS `xs`,
       round(((ifnull(`z`.`zje`, 0) - ifnull(`z`.`tchyf`, 0) - ifnull(`z`.`tcsp`, 0)) * ifnull(`x`.`lxs`, 0) - ifnull(`z`.`hz`, 0)) * ifnull(`x`.`xs`, 0), 2) AS `tc`
from dd_kz z
         left join kh k on z.khid = k.id,
     vw_ddtcxs x,
     vw_ddml m
where k.hzsj >= '2021-01-01'
  and k.hzsj < '2023-10-01'
  and z.cq < '2024-01-01'
  and z.ddbh = m.ddbh
  and m.mll > 5
  and z.ddbh = x.ddbh
union all
select `x`.`xm`,
       `z`.`cq`,
       `z`.`khid`                                                                                                                                                kh,
       `z`.`ddbh`,
       `z`.`tchyf`,
       `z`.`tcsp`,
       `z`.`zje`,
       `z`.`hz`,
       x.lxs,
       `z`.`zjedw`                                                                                                                                               hb,
       round((ifnull(`z`.`zje`, 0) - ifnull(`z`.`tchyf`, 0) - ifnull(`z`.`tcsp`, 0)) * ifnull(`x`.`lxs`, 0) - ifnull(`z`.`hz`, 0), 2)                         AS `rmb`,
       `x`.`xs`                                                                                                                                               AS `xs`,
       round(((ifnull(`z`.`zje`, 0) - ifnull(`z`.`tchyf`, 0) - ifnull(`z`.`tcsp`, 0)) * ifnull(`x`.`lxs`, 0) - ifnull(`z`.`hz`, 0)) * ifnull(`x`.`xs`, 0), 2) AS `tc`
from dd_kz z
         left join kh k on z.khid = k.id,
     vw_ddtcxs x,
     vw_ddml m
where (k.hzsj >= '2023-10-01' or k.hzsj is null or (k.hzsj>= '2021-01-01' and k.hzsj< '2023-10-01' and z.cq>='2024-01-01'))
  and z.ddbh = m.ddbh
  and m.lrmll > 5
  and z.ddbh = x.ddbh;

-- 实际回款的提成
create or replace view vw_tc as
select `x`.`xm`,
       `z`.`cq`,
       `z`.`khid`                                                                                                                                                kh,
       `z`.`ddbh`,
       `z`.`tchyf`,
       `z`.`tcsp`,
       `z`.`zje`,
       `z`.`hz`,
       x.lxs,
       `z`.`zjedw`                                                                                                                                               hb,
       case z.hkqk when '已回全款' then
           round((ifnull(z.zje, 0) - ifnull(`z`.`tchyf`, 0) - ifnull(`z`.`tcsp`, 0)) * ifnull(`x`.`lxs`, 0) - ifnull(`z`.`hz`, 0), 2)
       else round((ifnull(r.sjhk, 0) - ifnull(`z`.`tchyf`, 0) - ifnull(`z`.`tcsp`, 0)) * ifnull(`x`.`lxs`, 0) - ifnull(`z`.`hz`, 0), 2) end AS `rmb`,
       `x`.`xs`                                                                                                                                               AS `xs`,
       case z.hkqk when '已回全款' then
                       round(((ifnull(z.zje, 0) - ifnull(`z`.`tchyf`, 0) - ifnull(`z`.`tcsp`, 0)) * ifnull(`x`.`lxs`, 0) - ifnull(`z`.`hz`, 0))* ifnull(`x`.`xs`, 0), 2)
                   else round(((ifnull(r.sjhk, 0) - ifnull(`z`.`tchyf`, 0) - ifnull(`z`.`tcsp`, 0)) * ifnull(`x`.`lxs`, 0) - ifnull(`z`.`hz`, 0))* ifnull(`x`.`xs`, 0), 2) end AS `tc`
from dd_kz z
         left join kh k on z.khid = k.id,
     vw_ddtcxs x,
     vw_ddml m,
     vw_dd_hkrmb r
where k.hzsj < '2021-01-01'
  and z.ddbh = m.ddbh
  and z.ddbh = x.ddbh
  and z.ddbh=r.ddbh
  and z.hkqk='已回全款'
union all
select `x`.`xm`,
       `z`.`cq`,
       `z`.`khid`                                                                                                                                                kh,
       `z`.`ddbh`,
       `z`.`tchyf`,
       `z`.`tcsp`,
       `z`.`zje`,
       `z`.`hz`,
       x.lxs,
       `z`.`zjedw`                                                                                                                                               hb,
       case z.hkqk when '已回全款' then
                       round((ifnull(z.zje, 0) - ifnull(`z`.`tchyf`, 0) - ifnull(`z`.`tcsp`, 0)) * ifnull(`x`.`lxs`, 0) - ifnull(`z`.`hz`, 0), 2)
                   else round((ifnull(r.sjhk, 0) - ifnull(`z`.`tchyf`, 0) - ifnull(`z`.`tcsp`, 0)) * ifnull(`x`.`lxs`, 0) - ifnull(`z`.`hz`, 0), 2) end AS `rmb`,
       `x`.`xs`                                                                                                                                               AS `xs`,
       case z.hkqk when '已回全款' then
                       round(((ifnull(z.zje, 0) - ifnull(`z`.`tchyf`, 0) - ifnull(`z`.`tcsp`, 0)) * ifnull(`x`.`lxs`, 0) - ifnull(`z`.`hz`, 0))* ifnull(`x`.`xs`, 0), 2)
                   else round(((ifnull(r.sjhk, 0) - ifnull(`z`.`tchyf`, 0) - ifnull(`z`.`tcsp`, 0)) * ifnull(`x`.`lxs`, 0) - ifnull(`z`.`hz`, 0))* ifnull(`x`.`xs`, 0), 2) end AS `tc`
from dd_kz z
         left join kh k on z.khid = k.id,
     vw_ddtcxs x,
     vw_ddml m,
     vw_dd_hkrmb r
where k.hzsj >= '2021-01-01'
  and k.hzsj < '2023-10-01'
  and z.cq < '2024-01-01'
  and z.ddbh = m.ddbh
  and z.hkqk='已回全款'
  and m.mll > 5
  and z.ddbh = x.ddbh
  and z.ddbh=r.ddbh
union all
select `x`.`xm`,
       `z`.`cq`,
       `z`.`khid`                                                                                                                                                kh,
       `z`.`ddbh`,
       `z`.`tchyf`,
       `z`.`tcsp`,
       `z`.`zje`,
       `z`.`hz`,
       x.lxs,
       `z`.`zjedw`                                                                                                                                               hb,
       case z.hkqk when '已回全款' then
                       round((ifnull(z.zje, 0) - ifnull(`z`.`tchyf`, 0) - ifnull(`z`.`tcsp`, 0)) * ifnull(`x`.`lxs`, 0) - ifnull(`z`.`hz`, 0), 2)
                   else round((ifnull(r.sjhk, 0) - ifnull(`z`.`tchyf`, 0) - ifnull(`z`.`tcsp`, 0)) * ifnull(`x`.`lxs`, 0) - ifnull(`z`.`hz`, 0), 2) end AS `rmb`,
       `x`.`xs`                                                                                                                                               AS `xs`,
       case z.hkqk when '已回全款' then
                       round(((ifnull(z.zje, 0) - ifnull(`z`.`tchyf`, 0) - ifnull(`z`.`tcsp`, 0)) * ifnull(`x`.`lxs`, 0) - ifnull(`z`.`hz`, 0))* ifnull(`x`.`xs`, 0), 2)
                   else round(((ifnull(r.sjhk, 0) - ifnull(`z`.`tchyf`, 0) - ifnull(`z`.`tcsp`, 0)) * ifnull(`x`.`lxs`, 0) - ifnull(`z`.`hz`, 0))* ifnull(`x`.`xs`, 0), 2) end AS `tc`
from dd_kz z
         left join kh k on z.khid = k.id,
     vw_ddtcxs x,
     vw_ddml m,
     vw_dd_hkrmb r
where (k.hzsj >= '2023-10-01' or k.hzsj is null or (k.hzsj>= '2021-01-01' and k.hzsj< '2023-10-01' and z.cq>='2024-01-01'))
  and z.ddbh = m.ddbh
  and z.hkqk='已回全款'
  and m.xzmll > 5
  and z.ddbh = x.ddbh
  and z.ddbh=r.ddbh;

create or replace view vw_tc_0 as
select `a`.`xm`,
       `z`.`cq`,
       `z`.`khid`                                                                                                                                                kh,
       `z`.`ddbh`,
       `z`.`tchyf`,
       `z`.`tcsp`,
       `z`.`zje`,
       `z`.`hz`,
       l.xs lxs,
       `z`.`zjedw`                                                                                                                                               hb,
       case z.hkqk when '已回全款' then
                       round((ifnull(z.zje, 0) - ifnull(`z`.`tchyf`, 0) - ifnull(`z`.`tcsp`, 0)) * ifnull(`l`.`xs`, 0) - ifnull(`z`.`hz`, 0), 2)
                   else round((ifnull(r.sjhk, 0) - ifnull(`z`.`tchyf`, 0) - ifnull(`z`.`tcsp`, 0)) * ifnull(`l`.`xs`, 0) - ifnull(`z`.`hz`, 0), 2) end AS `rmb`,
       '0'                                                                                                                                               AS `xs`,
       '0' `tc`
from dd_kz z
        left join vw_ddml m on z.ddbh = m.ddbh
    left join vw_dd_hkrmb r on z.ddbh = r.ddbh
    left join lrhl l on z.zjedw = l.hb and z.cq>= l.kssj and z.cq<=l.jssj
    ,tc t left join account a on t.ywyid = a.id
where z.khid = t.khid and z.cq >= t.kssj and z.cq <= t.jssj and z.hkqk='已回全款' and z.ddbh not in (select ddbh from vw_tc);

create or replace view vw_tc_all as
    select * from vw_tc
union all
select * from vw_tc_0;

create or replace view vw_dk as
SELECT cjr,
       DATE(sj)                                                    AS dkrq,
       MIN(CASE WHEN TIME(sj) <= '12:00:00' THEN sj END)           AS sbdksj,
       MAX(CASE WHEN TIME(sj) > '12:00:00' THEN sj END)            AS xbdksj,
       TIMEDIFF(MAX(CASE WHEN TIME(sj) > '12:00:00' THEN sj END),
                MIN(CASE WHEN TIME(sj) <= '12:00:00' THEN sj END)) AS sbsc,
       TIMEDIFF(TIMEDIFF(MAX(CASE WHEN TIME(sj) > '12:00:00' THEN sj END),
                         MIN(CASE WHEN TIME(sj) <= '12:00:00' THEN sj END)),
                '09:30:00')                                        AS ccsbsc,
       TIMEDIFF(
               CASE
                   WHEN MIN(CASE WHEN TIME(sj) > '08:30:00' and TIME(sj) <= '12:00:00' THEN sj END) THEN TIME(sj)
                   END,
               '08:30:00'
       )                                                           AS cdsc,
       TIMEDIFF(
               '18:00:00',
               CASE
                   WHEN MAX(CASE WHEN TIME(sj) > '12:00:00' and TIME(sj) < '18:00:00' THEN sj END) THEN TIME(sj)
                   END
       )                                                           AS ztsc
FROM kq_dk
GROUP BY cjr, DATE(sj);


create or replace view vw_bx_zf as
select bxnr,
       sfkn,
       `km`   AS `km`,
       `fylb` as fylb,
       `jtfy` as jtfy,
       `ejkm` AS `ejkm`,
       `zfsj` AS `zfsj`,
       `zfje` AS `zfje`,
       `zffs` AS `zffs`
from (select bxnr,
             sfkn,
             `km`    AS `km`,
             `fylb`  as fylb,
             `jtfy`  as jtfy,
             `ejkm`  AS `ejkm`,
             `zfsj1` AS `zfsj`,
             `zfje1` AS `zfje`,
             `zffs1` AS `zffs`
      from `bx`
      union all
      select bxnr,
             sfkn,
             `km`    AS `km`,
             `fylb`  as fylb,
             `jtfy`  as jtfy,
             `ejkm`  AS `ejkm`,
             `zfsj2` AS `zfsj`,
             `zfje2` AS `zfje`,
             `zffs2` AS `zffs`
      from `bx`
      union all
      select bxnr,
             sfkn,
             `km`    AS `km`,
             `fylb`  as fylb,
             `jtfy`  as jtfy,
             `ejkm`  AS `ejkm`,
             `zfsj3` AS `zfsj`,
             `zfje3` AS `zfje`,
             `zffs3` AS `zffs`
      from `bx`) a;
create or replace view vw_wcbx_zf as
select bxnr, `km` AS `km`, `fylb` as fylb, `ejkm` AS `ejkm`, `zfsj` AS `zfsj`, `zfje` AS `zfje`, `zffs` AS `zffs`
from (select bxnr, `km` AS `km`, `fylb` as fylb, `ejkm` AS `ejkm`, `bxsj` AS `zfsj`, 0 - `bxje` AS `zfje`, '' AS `zffs`
      from `wcbx`
      where zfje1 is null
        and zfje2 is null
        and zfje3 is null
        and bxje is not null
      union all
      select bxnr, `km` AS `km`, `fylb` as fylb, `ejkm` AS `ejkm`, `zfsj1` AS `zfsj`, `zfje1` AS `zfje`, `zffs1` AS `zffs`
      from `wcbx`
      where zfje1 is not null
      union all
      select bxnr, `km` AS `km`, `fylb` as fylb, `ejkm` AS `ejkm`, `zfsj2` AS `zfsj`, `zfje2` AS `zfje`, `zffs2` AS `zffs`
      from `wcbx`
      where zfje2 is not null
      union all
      select bxnr, `km` AS `km`, `fylb` as fylb, `ejkm` AS `ejkm`, `zfsj3` AS `zfsj`, `zfje3` AS `zfje`, `zffs3` AS `zffs`
      from `wcbx`
      where zfje3 is not null) a;
create or replace view vw_gcbx_zf as
select bxnr, `fylb` as fylb, `zfsj` AS `zfsj`, `zfje` AS `zfje`, `zffs` AS `zffs`
from (select bxnr, `fylb` as fylb, `zfsj1` AS `zfsj`, `zfje1` AS `zfje`, `zffs1` AS `zffs`
      from `gcbx`
      union all
      select bxnr, `fylb` as fylb, `zfsj2` AS `zfsj`, `zfje2` AS `zfje`, `zffs2` AS `zffs`
      from `gcbx`
      union all
      select bxnr, `fylb` as fylb, `zfsj3` AS `zfsj`, `zfje3` AS `zfje`, `zffs3` AS `zffs`
      from `gcbx`) a;
create or replace view vw_bxzc as
select bxnr, `km` AS `km`, `ejkm` AS `ejkm`, `zfsj` AS `zfsj`, `zfje` AS `zfje`, `zffs` AS `zffs`
from (select bxnr, `km` AS `km`, `ejkm` AS `ejkm`, `zfsj1` AS `zfsj`, `zfje1` AS `zfje`, `zffs1` AS `zffs`
      from `bx`
      where (`zfsj1` >= '2023-01-01')
      union all
      select bxnr, `km` AS `km`, `ejkm` AS `ejkm`, `zfsj2` AS `zfsj`, `zfje2` AS `zfje`, `zffs2` AS `zffs`
      from `bx`
      where (`zfsj2` >= '2023-01-01')
      union all
      select bxnr, `km` AS `km`, `ejkm` AS `ejkm`, `zfsj3` AS `zfsj`, `zfje3` AS `zfje`, `zffs3` AS `zffs`
      from `bx`
      where (`zfsj3` >= '2023-01-01')
      union all
      select bxnr, `km` AS `km`, `ejkm` AS `ejkm`, `zfsj1` AS `zfsj`, `zfje1` AS `zfje`, `zffs1` AS `zffs`
      from `wcbx`
      where (`zfsj1` >= '2023-01-01')
      union all
      select bxnr, `km` AS `km`, `ejkm` AS `ejkm`, `zfsj2` AS `zfsj`, `zfje2` AS `zfje`, `zffs2` AS `zffs`
      from `wcbx`
      where (`zfsj2` >= '2023-01-01')
      union all
      select bxnr, `km` AS `km`, `ejkm` AS `ejkm`, `zfsj3` AS `zfsj`, `zfje3` AS `zfje`, `zffs3` AS `zffs`
      from `wcbx`
      where (`zfsj3` >= '2023-01-01')
      union all
      select concat(ddbh, ' ', bz) bxnr,
             `km`    AS            `km`,
             `ejkm`  AS            `ejkm`,
             `zfsj1` AS            `zfsj`,
             `zfje1` AS            `zfje`,
             `zffs1` AS            `zffs`
      from `kdf`
      where (`zfsj1` >= '2023-01-01')
      union all
      select concat(ddbh, ' ', bz) bxnr,
             `km`    AS            `km`,
             `ejkm`  AS            `ejkm`,
             `zfsj2` AS            `zfsj`,
             `zfje2` AS            `zfje`,
             `zffs2` AS            `zffs`
      from `kdf`
      where (`zfsj2` >= '2023-01-01')
      union all
      select concat(ddbh, ' ', bz) bxnr,
             `km`    AS            `km`,
             `ejkm`  AS            `ejkm`,
             `zfsj3` AS            `zfsj`,
             `zfje3` AS            `zfje`,
             `zffs3` AS            `zffs`
      from `kdf`
      where (`zfsj3` >= '2023-01-01')) `a`;

create or replace view vw_clcb_bc as
select `a`.`ddbh` AS `ddbh`, round(sum(`a`.`dcb`), 2) AS `cb`
from (select m.ddbh ddbh, IF(`m`.`zwszm` = '喷砂膜', (`m`.`sl` * cc.`jg`), (`m`.`cs` * cc.`jg`)) dcb
      from ddmx m
               left join dd d on m.ddbh = d.ddbh
               left join cl c on m.zwszm = c.mc
               left join cl_cb cc on c.id = cc.cl_id and d.cq >= cc.kssj and d.cq <= cc.jssj
      where (m.gc is null or m.gc like '%壹林%')) `a`
group by `a`.`ddbh`;

create or replace view vw_zf as
select *
from (select sj, je, fs, km, ejkm, zy
      from zf
      where sj >= '2023-01-01'
      union all
      select sj, zfje, zffs, zfkm, zfejkm, ''
      from sr
      where zfje is not null
        and sj >= '2023-01-01'
      union all
      select sj, zfje1, zffs1, zfkm1, zfejkm1, ''
      from sr
      where zfje1 is not null
        and sj >= '2023-01-01'
      union all
      select sj, zfje2, zffs2, zfkm2, zfejkm2, ''
      from sr
      where zfje2 is not null
        and sj >= '2023-01-01'
      union all
      select sj, zfje3, zffs3, zfkm3, zfejkm3, ''
      from sr
      where zfje3 is not null
        and sj >= '2023-01-01'
      union all
      select sj, zfje4, zffs4, zfkm4, zfejkm4, ''
      from sr
      where zfje4 is not null
        and sj >= '2023-01-01'
      union all
      select zfsj, zfje, zffs, zfkm, zfejkm, ''
      from gz
      where zfje is not null
        and zfsj >= '2023-01-01'
      union all
      select zfsj1, zfje1, zffs1, zfkm1, zfejkm1, ''
      from gz
      where zfje1 is not null
        and zfsj1 >= '2023-01-01'
      union all
      select zfsj2, zfje2, zffs2, zfkm2, zfejkm2, ''
      from gz
      where zfje2 is not null
        and zfsj2 >= '2023-01-01'
      union all
      select zfsj3, zfje3, zffs3, zfkm3, zfejkm3, ''
      from gz
      where zfje3 is not null
        and zfsj3 >= '2023-01-01'
      union all
      select zfsj4, zfje4, zffs4, zfkm4, zfejkm4, ''
      from gz
      where zfje4 is not null
        and zfsj4 >= '2023-01-01') a;

create or replace view vw_sr as
select *
from (select sj, je, fs, km, ejkm, zy
      from sr
      where sj >= '2023-01-01'
      union all
      select sj, srje, srfs, srkm, srejkm, ''
      from zf
      where srje is not null
        and sj >= '2023-01-01'
      union all
      select sj, srje, srfs, srkm, srejkm, ''
      from gz
      where srje is not null
        and sj >= '2023-01-01'
      union all
      select sj, srje1, srfs1, srkm1, srejkm1, ''
      from zf
      where srje1 is not null
        and sj >= '2023-01-01'
      union all
      select sj, srje2, srfs2, srkm2, srejkm2, ''
      from zf
      where srje2 is not null
        and sj >= '2023-01-01'
      union all
      select sj, srje3, srfs3, srkm3, srejkm3, ''
      from zf
      where srje3 is not null
        and sj >= '2023-01-01'
      union all
      select sj, srje4, srfs4, srkm4, srejkm4, ''
      from zf
      where srje4 is not null
        and sj >= '2023-01-01') a;


create or replace view vw_ddmx_zhj as
select ddbh,
       round(sum(ifnull(cs, 0)), 3)   cs,
       round(sum(ifnull(zl, 0)), 3)   zl,
       round(sum(ifnull(mhzj, 0)), 3) zje,
       max(ifnull(hb, ''))            zjedw
from ddmx
group by ddbh;

create or replace view vw_wjg_dm_fj as
select ddbh, round(sum(cs), 2) zcs, round(sum(je), 2) zje, (sum(je) - 20 * sum(cs)) cc, round(if((sum(je) > 20 * sum(cs)), (sum(je) - 20 * sum(cs)) * 1.3, 0), 2) dmfj
from (select yf,
             ddbh,
             ddid,
             max(ifnull(cs, 0)) cs,
             sum(ifnull(je, 0)) je
      from wjg_dm
      group by yf, ddbh, ddid) a
group by ddbh;

create or replace view vw_wjg_rm_fj as
select ddbh, round(sum(cs), 2) zcs, round(sum(je), 2) zje, (sum(je) - 20 * sum(cs)) cc, round(if((sum(je) > 20 * sum(cs)), (sum(je) - 20 * sum(cs)) * 1.3, 0), 2) rmfj
from (select yf,
             ddbh,
             ddid,
             max(ifnull(cs, 0)) cs,
             sum(ifnull(je, 0)) je
      from wjg_rm
      group by yf, ddbh, ddid) a
group by ddbh;

create or replace view vw_wjg_zx_fj as
select ddbh, round(sum(cs), 2) zcs, round(sum(je), 2) zje, (sum(je) - 20 * sum(cs)) cc, round(if((sum(je) > 20 * sum(cs)), (sum(je) - 20 * sum(cs)) * 1.3, 0), 2) zxfj
from (select yf,
             ddbh,
             ddid,
             max(ifnull(cs, 0)) cs,
             sum(ifnull(je, 0)) je
      from wjg_zx
      group by yf, ddbh, ddid) a
group by ddbh;

create or replace view vw_clfj as
select ddbh, round(sum(ifnull(clfj, 0)), 2) clfj
from (SELECT ddbh,
             CASE
                 WHEN LEAST(ifnull(sizeacm, 0) + 0, ifnull(sizebcm, 0) + 0, ifnull(sizeccm, 0) + 0) <= 1 THEN 100 * (ifnull(mhzj, 0) - ifnull(dpfjf, 0) * ifnull(sl, 0) - ifnull(kzfjf, 0) * ifnull(sl, 0)) / (100 + ifnull(clfj, 0))
                 WHEN LEAST(ifnull(sizeacm, 0) + 0, ifnull(sizebcm, 0) + 0, ifnull(sizeccm, 0) + 0) <= 2 THEN 50 * (ifnull(mhzj, 0) - ifnull(dpfjf, 0) * ifnull(sl, 0) - ifnull(kzfjf, 0) * ifnull(sl, 0)) / (100 + ifnull(clfj, 0))
                 WHEN LEAST(ifnull(sizeacm, 0) + 0, ifnull(sizebcm, 0) + 0, ifnull(sizeccm, 0) + 0) <= 3 THEN 30 * (ifnull(mhzj, 0) - ifnull(dpfjf, 0) * ifnull(sl, 0) - ifnull(kzfjf, 0) * ifnull(sl, 0)) / (100 + ifnull(clfj, 0))
                 WHEN LEAST(ifnull(sizeacm, 0) + 0, ifnull(sizebcm, 0) + 0, ifnull(sizeccm, 0) + 0) <= 4 THEN 20 * (ifnull(mhzj, 0) - ifnull(dpfjf, 0) * ifnull(sl, 0) - ifnull(kzfjf, 0) * ifnull(sl, 0)) / (100 + ifnull(clfj, 0))
                 WHEN LEAST(ifnull(sizeacm, 0) + 0, ifnull(sizebcm, 0) + 0, ifnull(sizeccm, 0) + 0) <= 5 THEN 10 * (ifnull(mhzj, 0) - ifnull(dpfjf, 0) * ifnull(sl, 0) - ifnull(kzfjf, 0) * ifnull(sl, 0)) / (100 + ifnull(clfj, 0))
                 ELSE 0 END AS clfj
      FROM ddmx
      where ddbh in (select ddbh from dd where lx = '正常订单')
        and LEAST(ifnull(sizeacm, 0) + 0, ifnull(sizebcm, 0) + 0, ifnull(sizeccm, 0) + 0) <= 5
        and LEAST(ifnull(sizeacm, 0) + 0, ifnull(sizebcm, 0) + 0, ifnull(sizeccm, 0) + 0) > 0
        and (gc is null or gc like '%壹林%') and ddbh in (select distinct ddbh from (select distinct ddbh from wjg union select distinct ddbh from wjg_dm union select distinct ddbh from wjg_rm union  select distinct ddbh from wjg_zx) ww)) a
group by ddbh;

create or replace view vw_clfj_mx as
SELECT ddbh,
       gcdd,
       zwszm,
       dw,
       sizea,
       sizeb,
       sizec,
       sizeacm,
       sizebcm,
       sizeccm,
       mhzj,
       100 * (ifnull(mhzj, 0) - ifnull(dpfjf, 0) * ifnull(sl, 0) - ifnull(kzfjf, 0) * ifnull(sl, 0)) / (100 + ifnull(clfj, 0)) clzj,
       LEAST(ifnull(sizeacm, 0) + 0, ifnull(sizebcm, 0) + 0, ifnull(sizeccm, 0) + 0)                                           zxcc,
       CASE
           WHEN LEAST(ifnull(sizeacm, 0) + 0, ifnull(sizebcm, 0) + 0, ifnull(sizeccm, 0) + 0) <= 1 THEN '100%'
           WHEN LEAST(ifnull(sizeacm, 0) + 0, ifnull(sizebcm, 0) + 0, ifnull(sizeccm, 0) + 0) <= 2 THEN '50%'
           WHEN LEAST(ifnull(sizeacm, 0) + 0, ifnull(sizebcm, 0) + 0, ifnull(sizeccm, 0) + 0) <= 3 THEN '30%'
           WHEN LEAST(ifnull(sizeacm, 0) + 0, ifnull(sizebcm, 0) + 0, ifnull(sizeccm, 0) + 0) <= 4 THEN '20%'
           WHEN LEAST(ifnull(sizeacm, 0) + 0, ifnull(sizebcm, 0) + 0, ifnull(sizeccm, 0) + 0) <= 5 THEN '10%'
           ELSE 0 END           AS                                                                                             xs,
       round(CASE
                 WHEN LEAST(ifnull(sizeacm, 0) + 0, ifnull(sizebcm, 0) + 0, ifnull(sizeccm, 0) + 0) <= 1 THEN 100 * (ifnull(mhzj, 0) - ifnull(dpfjf, 0) * ifnull(sl, 0) - ifnull(kzfjf, 0) * ifnull(sl, 0)) / (100 + ifnull(clfj, 0))
                 WHEN LEAST(ifnull(sizeacm, 0) + 0, ifnull(sizebcm, 0) + 0, ifnull(sizeccm, 0) + 0) <= 2 THEN 50 * (ifnull(mhzj, 0) - ifnull(dpfjf, 0) * ifnull(sl, 0) - ifnull(kzfjf, 0) * ifnull(sl, 0)) / (100 + ifnull(clfj, 0))
                 WHEN LEAST(ifnull(sizeacm, 0) + 0, ifnull(sizebcm, 0) + 0, ifnull(sizeccm, 0) + 0) <= 3 THEN 30 * (ifnull(mhzj, 0) - ifnull(dpfjf, 0) * ifnull(sl, 0) - ifnull(kzfjf, 0) * ifnull(sl, 0)) / (100 + ifnull(clfj, 0))
                 WHEN LEAST(ifnull(sizeacm, 0) + 0, ifnull(sizebcm, 0) + 0, ifnull(sizeccm, 0) + 0) <= 4 THEN 20 * (ifnull(mhzj, 0) - ifnull(dpfjf, 0) * ifnull(sl, 0) - ifnull(kzfjf, 0) * ifnull(sl, 0)) / (100 + ifnull(clfj, 0))
                 WHEN LEAST(ifnull(sizeacm, 0) + 0, ifnull(sizebcm, 0) + 0, ifnull(sizeccm, 0) + 0) <= 5 THEN 10 * (ifnull(mhzj, 0) - ifnull(dpfjf, 0) * ifnull(sl, 0) - ifnull(kzfjf, 0) * ifnull(sl, 0)) / (100 + ifnull(clfj, 0))
                 ELSE 0 END, 2) AS                                                                                             clfj
FROM ddmx
where ddbh in (select ddbh from dd where lx = '正常订单')
  and LEAST(ifnull(sizeacm, 0) + 0, ifnull(sizebcm, 0) + 0, ifnull(sizeccm, 0) + 0) <= 5
  and LEAST(ifnull(sizeacm, 0) + 0, ifnull(sizebcm, 0) + 0, ifnull(sizeccm, 0) + 0)
  and (gc is null or gc like '%壹林%') > 0 and ddbh in (select distinct ddbh from (select distinct ddbh from wjg union select distinct ddbh from wjg_dm union select distinct ddbh from wjg_rm union  select distinct ddbh from wjg_zx) ww);

create or replace view vw_pjmx as
select ddbh,
       xh,
       zwpm,
       ywpm,
       sl,
       jz,
       mz,
       xsizea,
       xsizeb,
       xsizec,
       '' bbsizea,
       '' bbsizeb,
       '' bbsizec,
       0 dj,
       0 mhzj,
       0 wcje,
       0 wcdj,
       hs,
       sbys
from pjmx
union all
select z.ddbh,
       z.xh,
       z.zwpm,
       z.ywpm,
       z.sl,
       z.jz,
       z.mz,
       z.xsizea,
       z.xsizeb,
       z.xsizec,
       ifnull(m.sizea, z.bsizea) bbsizea,
       ifnull(m.sizeb, z.bsizeb) bbsizeb,
       ifnull(m.sizec, z.bsizec) bbsizec,
       ifnull(m.dj, 0) dj,
       ifnull(m.mhzj, 0) mhzj,
       ifnull(m.wcje, 0) wcje,
       ifnull(m.wcje, 0)/ifnull(m.sl, 1) wcdj,
       m.hs,
       m.sbys
from zxdmx z,
     ddmx m
where z.ddbh = m.ddbh
  and z.ddid = m.id
  and m.hs is not null
  and m.sbys is not null
  and m.hs <> ''
  and m.sbys <> '';

-- 拼柜视图
create or replace view vw_dzhydp as
SELECT ddd.ddbh,dddz.zddbh,
       GROUP_CONCAT(
               CASE
                   WHEN FIND_IN_SET(ddd.ddbh, REPLACE(dddz.ddbh, ';', ',')) > 0 THEN dddz.ddbh
                   ELSE NULL
                   END
       ) AS ddbhs
FROM dzhy ddd
         LEFT JOIN dzhydp dddz ON FIND_IN_SET(ddd.ddbh, REPLACE(dddz.ddbh, ';', ','))
GROUP BY ddd.ddbh,dddz.zddbh;

create or replace view vw_ddmx_fz as
select ddbh, count(distinct gcdd) gcs, round(sum(ifnull(cs, 0)), 2) zcs, round(sum(ifnull(mhzj, 0)), 2) zje
from ddmx
where ddbh in (select ddbh from dd where lx <> '询价单')
group by ddbh;


create or replace view vw_answer as
select s.user_id, s.answer_date, ifnull(a.xm, a.username) xm, count(*) hds
from answer s left join account a on s.user_id=a.id
where ifnull(s.answer_content, '') <> ''
group by s.user_id, s.answer_date, a.xm;
-- 更新数据
# call gxbx_rb_gz_yxkh();
# call gx_wjg_fj();
# call pr_zcfz();
# call gxddgk();
